import { createFileRoute } from "@tanstack/react-router";
import { NewBlogPostPage } from "@/features/blog-management/components/NewBlogPostPage";
import { Loading } from "@/components/ui/loading";

export const Route = createFileRoute("/_authenticated/blog/new")({
  component: NewBlogPostPage,
  beforeLoad: () => {
    return {
      getTitle: () => 'Đăng tin'
    };
  },
  pendingComponent: () => <Loading />,
  errorComponent: () => <div className="p-10"><PERSON><PERSON> x<PERSON>y ra lỗi khi tải dữ liệu.</div>,
}); 