{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Information"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=yezhome;Username=postgres;Password=******"}, "Cors": {"AllowedOrigins": ["https://localhost:3000", "https://another-allowed-origin.com"]}, "AWS": {"Profile": "default", "Region": "ap-southeast-1", "AccessKey": "********************", "SecretKey": "SWPLEhCIHkMZqz31pPoeSh5FS3QelDALiwoUyjct", "SES": {"FromEmail": "<EMAIL>", "FromName": "YEZ Home"}}}