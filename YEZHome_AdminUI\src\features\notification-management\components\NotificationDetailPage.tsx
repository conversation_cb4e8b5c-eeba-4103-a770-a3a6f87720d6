import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Trash2, ExternalLink } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { Loading } from '@/components/ui/loading';
import notificationService from '@/services/notification-service';
import { notificationTypeMap } from '@/lib/types/notification';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

interface NotificationDetailPageProps {
  notificationId: string;
}

export const NotificationDetailPage = ({ notificationId }: NotificationDetailPageProps) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch notification details
  const { data: notification, isLoading, error } = useQuery({
    queryKey: ['notification', notificationId],
    queryFn: () => notificationService.getNotificationById(notificationId),
  });

  // Delete notification mutation
  const deleteNotificationMutation = useMutation({
    mutationFn: (id: string) => notificationService.deleteNotification(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      toast({
        title: 'Thành công',
        description: 'Thông báo đã được xóa thành công',
      });
      navigate({
        to: '/notification',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Lỗi',
        description: error.response?.data?.message || 'Có lỗi xảy ra khi xóa thông báo',
        variant: 'destructive',
      });
    },
  });

  const handleBack = () => {
    navigate({
      to: '/notification',
    });
  };

  const handleDelete = () => {
    deleteNotificationMutation.mutate(notificationId);
  };

  const getTypeVariant = (type: string): "blue" | "green" | "yellow" | "red" | "gray" | "purple" | "orange" | undefined => {
    switch (type) {
      case 'SYSTEM':
        return "blue";
      case 'PROMOTION':
        return "orange";
      case 'NEWS':
        return "green";
      default:
        return "gray";
    }
  };

  if (isLoading) {
    return <Loading />;
  }

  if (error || !notification) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={() => {
            history.go(-1); 
          }}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">Thông báo không tồn tại</h1>
        </div>
        <Card>
          <CardContent className="p-6">
            <p className="text-center text-gray-500">
              Không tìm thấy thông báo hoặc đã xảy ra lỗi khi tải dữ liệu.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={() => {
            history.go(-1); 
          }}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Chi tiết thông báo</h1>
            <p className="text-gray-600 mt-1">
              Xem thông tin chi tiết của thông báo
            </p>
          </div>
        </div>

        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button variant="destructive" className="gap-2">
              <Trash2 className="h-4 w-4" />
              Xóa thông báo
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Xác nhận xóa</AlertDialogTitle>
              <AlertDialogDescription>
                Bạn có chắc chắn muốn xóa thông báo này không? Hành động này không thể hoàn tác.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Hủy</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                className="bg-red-600 hover:bg-red-700"
              >
                Xóa
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <Badge
                  variant="subtle"
                  colorScheme={getTypeVariant(notification.type)}
                  showDot={true}
                  dotColor={getTypeVariant(notification.type)}
                >
                  {notificationTypeMap[notification.type] || notification.type}
                </Badge>
                {notification.title}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Nội dung</h3>
                  <p className="text-gray-700 whitespace-pre-wrap">
                    {notification.message}
                  </p>
                </div>

                {notification.actionUrl && (
                  <div>
                    <h3 className="font-medium text-gray-900 mb-2">URL hành động</h3>
                    <a
                      href={notification.actionUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800"
                    >
                      {notification.actionUrl}
                      <ExternalLink className="h-4 w-4" />
                    </a>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Thông tin chi tiết</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="text-sm font-medium text-gray-500">ID thông báo</div>
                <div className="text-sm text-gray-900 font-mono">{notification.id}</div>
              </div>

              <div>
                <div className="text-sm font-medium text-gray-500">Người nhận</div>
                <div className="text-sm text-gray-900 font-mono">{notification.userId}</div>
              </div>

              <div>
                <div className="text-sm font-medium text-gray-500">Trạng thái đọc</div>
                <Badge variant={notification.isRead ? 'default' : 'secondary'}>
                  {notification.isRead ? 'Đã đọc' : 'Chưa đọc'}
                </Badge>
              </div>

              <div>
                <div className="text-sm font-medium text-gray-500">Ngày tạo</div>
                <div className="text-sm text-gray-900">
                  {new Date(notification.createdAt).toLocaleDateString('vi-VN', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                  })}
                </div>
              </div>

              <div>
                <div className="text-sm font-medium text-gray-500">Ngày cập nhật</div>
                <div className="text-sm text-gray-900">
                  {new Date(notification.updatedAt).toLocaleDateString('vi-VN', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                  })}
                </div>
              </div>

              {notification.relatedEntityId && (
                <div>
                  <div className="text-sm font-medium text-gray-500">ID thực thể liên quan</div>
                  <div className="text-sm text-gray-900 font-mono">{notification.relatedEntityId}</div>
                </div>
              )}

              {notification.relatedPropertyId && (
                <div>
                  <div className="text-sm font-medium text-gray-500">ID bất động sản liên quan</div>
                  <div className="text-sm text-gray-900 font-mono">{notification.relatedPropertyId}</div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}; 