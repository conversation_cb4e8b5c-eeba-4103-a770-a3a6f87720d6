using Amazon.SimpleEmailV2;
using Amazon.SimpleEmailV2.Model;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using RealEstate.Application.Interfaces;

namespace RealEstate.Infrastructure.Services
{
    public class AwsSesEmailSender : IEmailSender
    {
        private readonly IAmazonSimpleEmailServiceV2 _sesClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<AwsSesEmailSender> _logger;

        public AwsSesEmailSender(
            IAmazonSimpleEmailServiceV2 sesClient,
            IConfiguration configuration,
            ILogger<AwsSesEmailSender> logger)
        {
            _sesClient = sesClient;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task SendEmailAsync(string to, string subject, string body)
        {
            try
            {
                var fromEmail = _configuration["AWS:SES:FromEmail"] ?? "<EMAIL>";

                var sendRequest = new SendEmailRequest
                {
                    FromEmailAddress = fromEmail,
                    Destination = new Destination
                    {
                        ToAddresses = new List<string> { to }
                    },
                    Content = new EmailContent
                    {
                        Simple = new Message
                        {
                            Subject = new Content
                            {
                                Data = subject,
                                Charset = "UTF-8"
                            },
                            Body = new Body
                            {
                                Html = new Content
                                {
                                    Data = body,
                                    Charset = "UTF-8"
                                }
                            }
                        }
                    }
                };

                _logger.LogInformation("Sending email to {To} with subject: {Subject}", to, subject);

                var response = await _sesClient.SendEmailAsync(sendRequest);

                _logger.LogInformation("Email sent successfully. MessageId: {MessageId}", response.MessageId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send email to {To} with subject: {Subject}", to, subject);
                throw;
            }
        }
    }
}
