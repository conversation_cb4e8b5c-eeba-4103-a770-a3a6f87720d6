using Amazon.SimpleEmailV2;
using Amazon.SimpleEmailV2.Model;
using RealEstate.Application.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RealEstate.Infrastructure.Services
{    public class AwsSesEmailSender : IEmailSender
    {
        private readonly IAmazonSimpleEmailServiceV2 _ses;

        public AwsSesEmailSender(IAmazonSimpleEmailServiceV2 ses)
        {
            _ses = ses;
        }

        public async Task SendEmailAsync(string to, string subject, string body)
        {
            var request = new SendEmailRequest
            {
                Source = "<EMAIL>",
                Destination = new Destination { ToAddresses = new List<string> { to } },
                Message = new Message
                {
                    Subject = new Content(subject),
                    Body = new Body { Html = new Content(body) }
                }
            };
            await _ses.SendEmailAsync(request);
        }
    }
}
