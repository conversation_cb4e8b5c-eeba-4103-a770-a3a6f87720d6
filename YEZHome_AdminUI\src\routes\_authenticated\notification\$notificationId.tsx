import { createFileRoute } from '@tanstack/react-router';
import { NotificationDetailPage } from '@/features/notification-management/components/NotificationDetailPage';
import { Loading } from '@/components/ui/loading';

export const Route = createFileRoute('/_authenticated/notification/$notificationId')({
  component: NotificationDetail,
  beforeLoad: () => {
    return {
      getTitle: () => 'Chi tiết thông báo'
    };
  },
  pendingComponent: () => <Loading />,
  errorComponent: () => <div className="p-10"><PERSON><PERSON> xảy ra lỗi khi tải dữ liệu.</div>,
});

function NotificationDetail() {
  const { notificationId } = Route.useParams();
  return <NotificationDetailPage notificationId={notificationId} />;
} 