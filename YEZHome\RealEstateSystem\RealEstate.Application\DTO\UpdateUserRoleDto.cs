﻿using System.ComponentModel.DataAnnotations;

namespace RealEstate.Application.DTO
{
    public class UpdateUserRoleDto
    {
        [Required]
        public Guid UserId { get; set; }
        
        [Required]
        public List<Guid> RoleIds { get; set; } = new List<Guid>();
    }

    public class UpdateUserStatusDto
    {
        [Required]
        public bool IsActive { get; set; }
    }
    public class UpdateUserPhoneDto
    {
        [Required]
        public Guid UserId { get; set; }

        [Required]
        [MaxLength(11, ErrorMessage = "<PERSON><PERSON><PERSON><PERSON> thoại không hợp lệ")]
        public string Phone { get; set; }
    }
}
