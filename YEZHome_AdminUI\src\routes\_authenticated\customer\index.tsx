import { createFileRoute, useNavigate } from '@tanstack/react-router'
import { useState, useMemo, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table'
import userService from '@/services/user-service'
import type { UserDto } from '@/lib/types/user'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Spinner } from '@/components/ui/spinner'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog'
import { Form } from '@/components/ui/form'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { MoreHorizontal, Eye, Trash2, Search } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { toast } from '@/components/ui/use-toast'
import { PAGE_SIZE } from '@/lib/enum'
import { Loading } from '@/components/ui/loading'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

// Define search schema for URL parameters
const customerSearchSchema = z.object({
  page: z.number().optional().default(1),
  pageSize: z.number().optional().default(PAGE_SIZE),
  email: z.string().optional().default(''),
  name: z.string().optional().default(''),
  phone: z.string().optional().default(''),
  sortColumn: z.string().optional(),
  sortDescending: z.boolean().optional(),
})

type CustomerSearchParams = z.infer<typeof customerSearchSchema>

export const Route = createFileRoute('/_authenticated/customer/')({
  validateSearch: customerSearchSchema,
  component: Customers,
  beforeLoad: () => {
    return {
      getTitle: () => 'Quản lý khách hàng'
    };
  },
  pendingComponent: () => <Loading />,
  errorComponent: () => <div className="p-10">Đã xảy ra lỗi khi tải dữ liệu.</div>,
})

function Customers() {
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const search = Route.useSearch()

  // State for dialogs
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedCustomer, setSelectedCustomer] = useState<UserDto | null>(null)

  // Convert search params to the format expected by the API
  const searchParams = useMemo(() => ({
    PageNumber: search.page,
    PageSize: search.pageSize,
    Email: search.email || undefined,
    Name: search.name || undefined,
    Phone: search.phone || undefined,
    SortColumn: search.sortColumn,
    SortDescending: search.sortDescending,
  }), [search])

  // Search form
  const searchForm = useForm<CustomerSearchParams>({
    defaultValues: {
      email: search.email,
      name: search.name,
      phone: search.phone,
    },
  })

  // Fetch customers based on search params
  const { data: customersData, isLoading: isLoadingCustomers } = useQuery({
    queryKey: ['customers', searchParams],
    queryFn: () => userService.getCustomers(searchParams),
  })

  const deleteCustomerMutation = useMutation({
    mutationFn: (id: string) => userService.deleteUser(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customers'] })
      setIsDeleteDialogOpen(false)
      setSelectedCustomer(null)
      toast({
        title: "Thành công",
        description: "Đã xóa khách hàng",
        variant: "success",
      })
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: "Không thể xóa khách hàng. Vui lòng thử lại.",
        variant: "destructive",
      })
      console.error('Error deleting customer:', error)
    }
  })

  // Update search form when search params change (e.g., when navigating back)
  useEffect(() => {
    searchForm.reset({
      email: search.email,
      name: search.name,
      phone: search.phone,
    })
  }, [search, searchForm])

  // Handle search submission
  const onSubmitSearch = (data: CustomerSearchParams) => {
    navigate({
      to: '/customer',
      search: {
        ...search,
        ...data,
        page: 1, // Reset to first page on new search
      }
    })
  }

  // Row action handlers
  const handleViewDetails = (customer: UserDto) => {
    navigate({
      to: '/customer/$customerId',
      params: { customerId: customer.id },
      search: {
        from: 'customer',
        filters: JSON.stringify(searchParams)
      }
    })
  }

  const handleDelete = (customer: UserDto) => {
    setSelectedCustomer(customer)
    setIsDeleteDialogOpen(true)
  }

  const confirmDelete = () => {
    if (selectedCustomer?.id) {
      deleteCustomerMutation.mutate(selectedCustomer.id)
    }
  }

  // Define table columns
  const columnHelper = createColumnHelper<UserDto>()
  const columns = useMemo(() => [
    columnHelper.accessor('fullName', {
      header: 'Họ tên',
      cell: info => info.getValue() || '-',
    }),
    columnHelper.accessor('email', {
      header: 'Email',
      cell: info => info.getValue() || '-',
    }),
    columnHelper.accessor('phone', {
      header: 'Số điện thoại',
      cell: info => info.getValue() || '-',
    }),
    columnHelper.accessor('userType', {
      header: 'Loại người dùng',
      cell: info => info.getValue() || '-',
    }),
    columnHelper.accessor('isActive', {
      header: 'Trạng thái',
      cell: info => {
        const isActive = info.getValue();
        return (
          <Badge
            variant="subtle"
            colorScheme={isActive ? "green" : "red"}
            className="whitespace-nowrap"
          >
            {isActive ? 'Đang hoạt động' : 'Đã vô hiệu hóa'}
          </Badge>
        );
      },
    }),
    columnHelper.accessor('lastLogin', {
      header: 'Ngày đăng nhập cuối',
      cell: info => {
        const date = info.getValue();
        return date ? new Date(date).toLocaleDateString('vi-VN') : '-';
      },
    }),
    columnHelper.display({
      id: 'actions',
      header: '',
      cell: info => {
        const customer = info.row.original;
        return (
          <div className="text-right">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => handleViewDetails(customer)}>
                  <Eye className="mr-2 h-4 w-4" />
                  <span>Xem chi tiết</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleDelete(customer)} className="text-red-600">
                  <Trash2 className="mr-2 h-4 w-4" />
                  <span>Xóa</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    }),
  ], [])

  // Initialize table
  const table = useReactTable({
    data: customersData?.items || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: {
        pageSize: search.pageSize,
        pageIndex: search.page - 1,
      },
    },
    manualPagination: true,
    pageCount: customersData?.pageCount || -1,
  })

  // Handle pagination
  const handlePageChange = (pageIndex: number) => {
    navigate({
      to: '/customer',
      search: {
        ...search,
        page: pageIndex + 1,
      },
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Quản lý khách hàng</h1>
      </div>
      
      {/* Search Form */}
      <Card>
        <CardContent>
          <Form {...searchForm}>
            <form onSubmit={searchForm.handleSubmit(onSubmitSearch)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    placeholder="Nhập email"
                    {...searchForm.register('email')}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="name">Họ tên</Label>
                  <Input
                    id="name"
                    placeholder="Nhập họ tên"
                    {...searchForm.register('name')}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">Số điện thoại</Label>
                  <Input
                    id="phone"
                    placeholder="Nhập số điện thoại"
                    {...searchForm.register('phone')}
                  />
                </div>
              </div>
              <div className="flex justify-end">
                <Button type="submit" className="flex items-center">
                  <Search className="mr-2 h-4 w-4" />
                  Tìm kiếm
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* Customers Table */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách khách hàng</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingCustomers ? (
            <div className="flex justify-center items-center h-64">
              <Spinner />
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader className="bg-muted[&_tr]:border-b bg-muted sticky top-0 z-10">
                    {table.getHeaderGroups().map(headerGroup => (
                      <TableRow key={headerGroup.id}>
                        {headerGroup.headers.map(header => (
                          <TableHead key={header.id}>
                            {header.isPlaceholder
                              ? null
                              : flexRender(
                                  header.column.columnDef.header,
                                  header.getContext()
                                )}
                          </TableHead>
                        ))}
                      </TableRow>
                    ))}
                  </TableHeader>
                  <TableBody>
                    {table.getRowModel().rows.length > 0 ? (
                      table.getRowModel().rows.map(row => (
                        <TableRow key={row.id}>
                          {row.getVisibleCells().map(cell => (
                            <TableCell key={cell.id}>
                              {flexRender(cell.column.columnDef.cell, cell.getContext())}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={columns.length} className="h-24 text-center">
                          Không có dữ liệu
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              <div className="flex items-center justify-between py-4">
                <div className="text-sm text-muted-foreground">
                  Hiển thị {table.getRowModel().rows.length} trên {customersData?.totalCount || 0} khách hàng
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(0)}
                    disabled={!customersData?.hasPreviousPage}
                  >
                    Đầu
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(search.page - 2)}
                    disabled={!customersData?.hasPreviousPage}
                  >
                    Trước
                  </Button>
                  <span className="text-sm">
                    Trang {search.page} / {customersData?.pageCount || 1}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(search.page)}
                    disabled={!customersData?.hasNextPage}
                  >
                    Sau
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(customersData ? customersData.pageCount - 1 : 0)}
                    disabled={!customersData?.hasNextPage}
                  >
                    Cuối
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận xóa khách hàng</AlertDialogTitle>
            <AlertDialogDescription>
              Bạn có chắc chắn muốn xóa khách hàng này? Hành động này không thể hoàn tác.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Thoát</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              Xác nhận xóa
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
