import{j as t,O as e,Q as n,S as o,T as a,P as i}from"./index-DFXNGaQ4.js";e({page:a().optional().default(1),pageSize:a().optional().default(i),email:o().optional().default(""),name:o().optional().default(""),phone:o().optional().default(""),sortColumn:o().optional(),sortDescending:n().optional()});const p=()=>t.jsx("div",{className:"p-10",children:"<PERSON><PERSON> xảy ra lỗi khi tải dữ liệu."});export{p as errorComponent};
