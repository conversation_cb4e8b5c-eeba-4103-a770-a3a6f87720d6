import { useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table';
import notificationService from '@/services/notification-service';
import type { Notification, NotificationFilter } from '@/lib/types/notification';
import { notificationTypeMap } from '@/lib/types/notification';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { MoreHorizontal, Eye, Trash2 } from 'lucide-react';
import { Loading } from '@/components/ui/loading';
import { NOTIFICATION_TYPE } from '@/lib/enum';

interface NotificationsTableProps {
  filters: NotificationFilter;
  onPageChange: (page: number) => void;
  onViewDetails: (id: string) => void;
  onDelete: (id: string) => void;
}

export const NotificationsTable = ({ 
  filters, 
  onPageChange, 
  onViewDetails, 
  onDelete 
}: NotificationsTableProps) => {
  // Fetch notifications based on filters
  const { data: notificationsData, isLoading } = useQuery({
    queryKey: ['notifications', filters],
    queryFn: () => notificationService.getNotifications(filters),
  });

  // Define table columns
  const columnHelper = createColumnHelper<Notification>();
  const columns = useMemo(() => [
    columnHelper.accessor('type', {
      header: 'Loại',
      cell: info => {
        const value = info.getValue();
        const variant = getTypeVariant(value);
        return (
          <div className="w-24">
            <Badge
              variant="subtle"
              colorScheme={variant}
              className="whitespace-nowrap overflow-hidden text-ellipsis max-w-full"
              showDot={true}
              dotColor={variant}
              title={notificationTypeMap[value] || value}
            >
              {notificationTypeMap[value] || value}
            </Badge>
          </div>
        );
      },
    }),
    columnHelper.accessor('title', {
      header: 'Tiêu đề',
      cell: info => (
        <div className="max-w-48 truncate font-medium" title={info.getValue()}>
          {info.getValue()}
        </div>
      ),
    }),
    columnHelper.accessor('message', {
      header: 'Nội dung',
      cell: info => (
        <div className="max-w-64 truncate text-sm text-gray-600" title={info.getValue()}>
          {info.getValue()}
        </div>
      ),
    }),
    columnHelper.accessor('actionUrl', {
      header: 'URL hành động',
      cell: info => {
        const url = info.getValue();
        return url ? (
          <a
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 hover:text-blue-800 text-sm truncate max-w-32 block"
            title={url}
          >
            {url}
          </a>
        ) : (
          <span className="text-gray-400 text-sm">-</span>
        );
      },
    }),
    columnHelper.accessor('createdAt', {
      header: 'Ngày tạo',
      cell: info => new Date(info.getValue()).toLocaleDateString('vi-VN'),
    }),
    columnHelper.display({
      id: 'actions',
      header: '',
      cell: info => {
        const id = info.row.original.id;
        return (
          <div className="text-right">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onViewDetails(id)}>
                  <Eye className="mr-2 h-4 w-4" />
                  <span>Xem chi tiết</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onDelete(id)} className="text-red-600">
                  <Trash2 className="mr-2 h-4 w-4" />
                  <span>Xóa</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    }),
  ], [columnHelper, onViewDetails, onDelete]);

  // Initialize table
  const table = useReactTable({
    data: notificationsData?.items || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    pageCount: notificationsData?.pageCount || -1,
    manualPagination: true,
    state: {
      pagination: {
        pageIndex: (filters.page || 1) - 1,
        pageSize: filters.pageSize || 10,
      },
    },
    onPaginationChange: (updater) => {
      if (typeof updater === 'function') {
        const newPagination = updater(table.getState().pagination);
        onPageChange(newPagination.pageIndex + 1);
      } else {
        onPageChange(updater.pageIndex + 1);
      }
    },
  });

  // Helper function to determine badge variant based on notification type
  const getTypeVariant = (type: string): "blue" | "green" | "yellow" | "red" | "gray" | "purple" | "orange" | undefined => {
    switch (type) {
      case NOTIFICATION_TYPE.SYSTEM:
        return "blue";
      case NOTIFICATION_TYPE.PROMOTION:
        return "orange";
      case NOTIFICATION_TYPE.NEWS:
        return "green";
      default:
        return "gray";
    }
  };

  if (isLoading) {
    return <Loading />;
  }

  return (
    <div className="space-y-4">
      <div className="border rounded-md">
        <Table className="w-full caption-bottom text-sm">
          <TableHeader className="bg-muted[&_tr]:border-b bg-muted sticky top-0 z-10">
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow key={row.id}>
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id} className="whitespace-normal">
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  Không có dữ liệu
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between py-4">
        <div className="text-sm text-muted-foreground">
          Hiển thị {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1} đến{' '}
          {Math.min(
            (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
            notificationsData?.totalCount || 0
          )}{' '}
          trong tổng số {notificationsData?.totalCount || 0} kết quả
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Trước
          </Button>
          <span className="text-sm">
            Trang {table.getState().pagination.pageIndex + 1} / {notificationsData?.pageCount || 1}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Sau
          </Button>
        </div>
      </div>
    </div>
  );
}; 