import{r as o,j as s,N as t}from"./index-DFXNGaQ4.js";const i=o.forwardRef(({className:e,...r},a)=>s.jsx("textarea",{className:t("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...r}));i.displayName="Textarea";function d(e,[r,a]){return Math.min(a,Math.max(r,e))}export{i as T,d as c};
