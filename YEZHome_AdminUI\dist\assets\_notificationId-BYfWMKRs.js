import{c as D,a as C,k,l as A,b as T,m as w,j as e,L,B as n,aj as I}from"./index-DFXNGaQ4.js";import{C as r,d as c,a as m,b as g}from"./card-Dikj3j-6.js";import{B as j}from"./badge-BmdoMzu9.js";import{a as u,n as q}from"./notification-BvCoxbDh.js";import{A as S,h as E,a as M,b as B,c as R,d as H,e as P,f as Q,g as U}from"./alert-dialog-CINOfzqV.js";import{A as y}from"./arrow-left-CbfRM3Sk.js";import{T as V}from"./trash-2-BZu0p0bP.js";/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const X=[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]],F=D("ExternalLink",X),K=({notificationId:a})=>{const i=C(),{toast:l}=k(),N=A(),{data:t,isLoading:p,error:v}=T({queryKey:["notification",a],queryFn:()=>u.getNotificationById(a)}),f=w({mutationFn:s=>u.deleteNotification(s),onSuccess:()=>{N.invalidateQueries({queryKey:["notifications"]}),l({title:"Thành công",description:"Thông báo đã được xóa thành công"}),i({to:"/notification"})},onError:s=>{var h,x;l({title:"Lỗi",description:((x=(h=s.response)==null?void 0:h.data)==null?void 0:x.message)||"Có lỗi xảy ra khi xóa thông báo",variant:"destructive"})}}),d=()=>{i({to:"/notification"})},b=()=>{f.mutate(a)},o=s=>{switch(s){case"SYSTEM":return"blue";case"PROMOTION":return"orange";case"NEWS":return"green";default:return"gray"}};return p?e.jsx(L,{}):v||!t?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(n,{variant:"outline",size:"sm",onClick:d,className:"gap-2",children:[e.jsx(y,{className:"h-4 w-4"}),"Quay lại"]}),e.jsx("h1",{className:"text-2xl font-bold",children:"Thông báo không tồn tại"})]}),e.jsx(r,{children:e.jsx(c,{className:"p-6",children:e.jsx("p",{className:"text-center text-gray-500",children:"Không tìm thấy thông báo hoặc đã xảy ra lỗi khi tải dữ liệu."})})})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(n,{variant:"outline",size:"sm",onClick:d,className:"gap-2",children:[e.jsx(y,{className:"h-4 w-4"}),"Quay lại"]}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Chi tiết thông báo"}),e.jsx("p",{className:"text-gray-600 mt-1",children:"Xem thông tin chi tiết của thông báo"})]})]}),e.jsxs(S,{children:[e.jsx(E,{asChild:!0,children:e.jsxs(n,{variant:"destructive",className:"gap-2",children:[e.jsx(V,{className:"h-4 w-4"}),"Xóa thông báo"]})}),e.jsxs(M,{children:[e.jsxs(B,{children:[e.jsx(R,{children:"Xác nhận xóa"}),e.jsx(H,{children:"Bạn có chắc chắn muốn xóa thông báo này không? Hành động này không thể hoàn tác."})]}),e.jsxs(P,{children:[e.jsx(Q,{children:"Hủy"}),e.jsx(U,{onClick:b,className:"bg-red-600 hover:bg-red-700",children:"Xóa"})]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsx("div",{className:"lg:col-span-2 space-y-6",children:e.jsxs(r,{children:[e.jsx(m,{children:e.jsxs(g,{className:"flex items-center gap-3",children:[e.jsx(j,{variant:"subtle",colorScheme:o(t.type),showDot:!0,dotColor:o(t.type),children:q[t.type]||t.type}),t.title]})}),e.jsx(c,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900 mb-2",children:"Nội dung"}),e.jsx("p",{className:"text-gray-700 whitespace-pre-wrap",children:t.message})]}),t.actionUrl&&e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-900 mb-2",children:"URL hành động"}),e.jsxs("a",{href:t.actionUrl,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-2 text-blue-600 hover:text-blue-800",children:[t.actionUrl,e.jsx(F,{className:"h-4 w-4"})]})]})]})})]})}),e.jsx("div",{className:"space-y-6",children:e.jsxs(r,{children:[e.jsx(m,{children:e.jsx(g,{className:"text-lg",children:"Thông tin chi tiết"})}),e.jsxs(c,{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-500",children:"ID thông báo"}),e.jsx("div",{className:"text-sm text-gray-900 font-mono",children:t.id})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-500",children:"Người nhận"}),e.jsx("div",{className:"text-sm text-gray-900 font-mono",children:t.userId})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-500",children:"Trạng thái đọc"}),e.jsx(j,{variant:t.isRead?"default":"secondary",children:t.isRead?"Đã đọc":"Chưa đọc"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-500",children:"Ngày tạo"}),e.jsx("div",{className:"text-sm text-gray-900",children:new Date(t.createdAt).toLocaleDateString("vi-VN",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-500",children:"Ngày cập nhật"}),e.jsx("div",{className:"text-sm text-gray-900",children:new Date(t.updatedAt).toLocaleDateString("vi-VN",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})})]}),t.relatedEntityId&&e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-500",children:"ID thực thể liên quan"}),e.jsx("div",{className:"text-sm text-gray-900 font-mono",children:t.relatedEntityId})]}),t.relatedPropertyId&&e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-gray-500",children:"ID bất động sản liên quan"}),e.jsx("div",{className:"text-sm text-gray-900 font-mono",children:t.relatedPropertyId})]})]})]})})]})]})},Z=function(){const{notificationId:i}=I.useParams();return e.jsx(K,{notificationId:i})};export{Z as component};
