import { createFileRoute } from '@tanstack/react-router';
import { NotificationPage } from '@/features/notification-management/components/NotificationPage';
import { Loading } from '@/components/ui/loading';

export const Route = createFileRoute('/_authenticated/notification/')({
  component: NotificationPage,
  beforeLoad: () => {
    return {
      getTitle: () => 'Quản lý thông báo'
    };
  },
  pendingComponent: () => <Loading />,
  errorComponent: () => <div className="p-10"><PERSON><PERSON> x<PERSON>y ra lỗi khi tải dữ liệu.</div>,
});
