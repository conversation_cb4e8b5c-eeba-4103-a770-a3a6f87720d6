import{r as s,am as A,j as t,ab as O,w as D,an as $,t as M,ac as I,y as z,ao as F,ae as L,ap as G,ad as v,af as k,ag as q,aq as H,N as i,ar as m}from"./index-DFXNGaQ4.js";var x="AlertDialog",[V,ge]=M(x,[A]),n=A(),N=e=>{const{__scopeAlertDialog:a,...o}=e,r=n(a);return t.jsx(O,{...r,...o,modal:!0})};N.displayName=x;var W="AlertDialogTrigger",y=s.forwardRef((e,a)=>{const{__scopeAlertDialog:o,...r}=e,l=n(o);return t.jsx(H,{...l,...r,ref:a})});y.displayName=W;var Y="AlertDialogPortal",j=e=>{const{__scopeAlertDialog:a,...o}=e,r=n(a);return t.jsx(k,{...r,...o})};j.displayName=Y;var B="AlertDialogOverlay",_=s.forwardRef((e,a)=>{const{__scopeAlertDialog:o,...r}=e,l=n(o);return t.jsx(q,{...l,...r,ref:a})});_.displayName=B;var c="AlertDialogContent",[J,K]=V(c),Q=F("AlertDialogContent"),h=s.forwardRef((e,a)=>{const{__scopeAlertDialog:o,children:r,...l}=e,p=n(o),g=s.useRef(null),w=D(a,g),u=s.useRef(null);return t.jsx($,{contentName:c,titleName:b,docsSlug:"alert-dialog",children:t.jsx(J,{scope:o,cancelRef:u,children:t.jsxs(I,{role:"alertdialog",...p,...l,ref:w,onOpenAutoFocus:z(l.onOpenAutoFocus,d=>{var f;d.preventDefault(),(f=u.current)==null||f.focus({preventScroll:!0})}),onPointerDownOutside:d=>d.preventDefault(),onInteractOutside:d=>d.preventDefault(),children:[t.jsx(Q,{children:r}),t.jsx(X,{contentRef:g})]})})})});h.displayName=c;var b="AlertDialogTitle",C=s.forwardRef((e,a)=>{const{__scopeAlertDialog:o,...r}=e,l=n(o);return t.jsx(L,{...l,...r,ref:a})});C.displayName=b;var E="AlertDialogDescription",R=s.forwardRef((e,a)=>{const{__scopeAlertDialog:o,...r}=e,l=n(o);return t.jsx(G,{...l,...r,ref:a})});R.displayName=E;var U="AlertDialogAction",S=s.forwardRef((e,a)=>{const{__scopeAlertDialog:o,...r}=e,l=n(o);return t.jsx(v,{...l,...r,ref:a})});S.displayName=U;var P="AlertDialogCancel",T=s.forwardRef((e,a)=>{const{__scopeAlertDialog:o,...r}=e,{cancelRef:l}=K(P,o),p=n(o),g=D(a,l);return t.jsx(v,{...p,...r,ref:g})});T.displayName=P;var X=({contentRef:e})=>{const a=`\`${c}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${c}\` by passing a \`${E}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${c}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return s.useEffect(()=>{var r;document.getElementById((r=e.current)==null?void 0:r.getAttribute("aria-describedby"))||console.warn(a)},[a,e]),null},Z=N,ee=y,ae=j,te=_,oe=h,re=S,le=T,se=C,ne=R;function pe({...e}){return t.jsx(Z,{"data-slot":"alert-dialog",...e})}function ue({...e}){return t.jsx(ee,{"data-slot":"alert-dialog-trigger",...e})}function ie({...e}){return t.jsx(ae,{"data-slot":"alert-dialog-portal",...e})}function ce({className:e,...a}){return t.jsx(te,{"data-slot":"alert-dialog-overlay",className:i("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...a})}function fe({className:e,...a}){return t.jsxs(ie,{children:[t.jsx(ce,{}),t.jsx(oe,{"data-slot":"alert-dialog-content",className:i("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...a})]})}function Ae({className:e,...a}){return t.jsx("div",{"data-slot":"alert-dialog-header",className:i("flex flex-col gap-2 text-center sm:text-left",e),...a})}function De({className:e,...a}){return t.jsx("div",{"data-slot":"alert-dialog-footer",className:i("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...a})}function ve({className:e,...a}){return t.jsx(se,{"data-slot":"alert-dialog-title",className:i("text-lg font-semibold",e),...a})}function me({className:e,...a}){return t.jsx(ne,{"data-slot":"alert-dialog-description",className:i("text-muted-foreground text-sm",e),...a})}function xe({className:e,...a}){return t.jsx(re,{className:i(m(),e),...a})}function Ne({className:e,...a}){return t.jsx(le,{className:i(m({variant:"outline"}),e),...a})}export{pe as A,fe as a,Ae as b,ve as c,me as d,De as e,Ne as f,xe as g,ue as h};
