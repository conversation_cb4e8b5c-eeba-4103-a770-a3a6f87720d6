import { useMemo } from 'react'
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table'
import { MoreHorizontal, Eye, Edit, Trash2 } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Spinner } from '@/components/ui/spinner'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import type { UserDto } from '@/lib/types/user'

interface EmployeeTableProps {
  data: UserDto[]
  isLoading: boolean
  totalCount: number
  pageCount: number
  currentPage: number
  pageSize: number
  onPageChange: (page: number, size: number) => void
  onView: (employee: UserDto) => void
  onEdit: (employee: UserDto) => void
  onDelete: (employee: UserDto) => void
}

export const EmployeeTable = ({
  data,
  isLoading,
  totalCount,
  pageCount,
  currentPage,
  pageSize,
  onPageChange,
  onView,
  onEdit,
  onDelete
}: EmployeeTableProps) => {
  const columnHelper = createColumnHelper<UserDto>()
  
  const columns = useMemo(() => [
    columnHelper.accessor('fullName', {
      header: 'Họ tên',
      cell: info => info.getValue() || '-',
    }),
    columnHelper.accessor('email', {
      header: 'Email',
      cell: info => info.getValue() || '-',
    }),
    columnHelper.accessor('phone', {
      header: 'Số điện thoại',
      cell: info => info.getValue() || '-',
    }),
    columnHelper.accessor('userType', {
      header: 'Loại người dùng',
      cell: info => info.getValue() || '-',
    }),
    columnHelper.accessor('roleObjects', {
      header: 'Vai trò',
      cell: info => info.getValue()?.map(role => role.roleName).join(', ') || '-',
    }),
    columnHelper.accessor('isActive', {
      header: 'Trạng thái',
      cell: info => {
        const isActive = info.getValue()
        return (
          <Badge variant="subtle" colorScheme={isActive ? "green" : "red"}>
            {isActive ? 'Đang hoạt động' : 'Đã vô hiệu hóa'}
          </Badge>
        )
      },
    }),
    columnHelper.accessor(row => row.lastLogin ? new Date(row.lastLogin).toLocaleDateString('vi-VN') : '-', {
      id: 'lastLogin',
      header: 'Đăng nhập cuối',
      cell: info => info.getValue(),
    }),
    columnHelper.display({
      id: 'actions',
      header: '',
      cell: info => {
        const employee = info.row.original;
        return (
          <div className="text-right">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onView(employee)}>
                  <Eye className="mr-2 h-4 w-4" />
                  <span>Xem chi tiết</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onEdit(employee)}>
                  <Edit className="mr-2 h-4 w-4" />
                  <span>Chỉnh sửa</span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onDelete(employee)} className="text-red-600">
                  <Trash2 className="mr-2 h-4 w-4" />
                  <span>Xóa</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    }),
  ], [onView, onEdit, onDelete])

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    pageCount,
    manualPagination: true,
    state: {
      pagination: {
        pageIndex: currentPage - 1,
        pageSize,
      },
    },
    onPaginationChange: (updater) => {
      if (typeof updater === 'function') {
        const newPagination = updater(table.getState().pagination)
        onPageChange(newPagination.pageIndex + 1, newPagination.pageSize)
      } else {
        onPageChange(updater.pageIndex + 1, updater.pageSize)
      }
    },
  })

  return (
    <Card>
      <CardHeader>
        <CardTitle>Danh sách nhân viên</CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center py-8">
            <Spinner />
          </div>
        ) : (
          <>
            <div className="rounded-md border">
              <Table>
                <TableHeader className="bg-muted[&_tr]:border-b bg-muted sticky top-0 z-10">
                  {table.getHeaderGroups().map(headerGroup => (
                    <TableRow key={headerGroup.id}>
                      {headerGroup.headers.map(header => (
                        <TableHead key={header.id}>
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                        </TableHead>
                      ))}
                    </TableRow>
                  ))}
                </TableHeader>
                <TableBody>
                  {table.getRowModel().rows.length ? (
                    table.getRowModel().rows.map(row => (
                      <TableRow key={row.id}>
                        {row.getVisibleCells().map(cell => (
                          <TableCell key={cell.id}>
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={columns.length} className="h-24 text-center">
                        Không có dữ liệu
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            <div className="flex items-center justify-between py-4">
              <div className="text-sm text-muted-foreground">
                Hiển thị {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1} đến{' '}
                {Math.min(
                  (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
                  totalCount
                )}{' '}
                trong tổng số {totalCount} kết quả
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => table.previousPage()}
                  disabled={!table.getCanPreviousPage()}
                >
                  Trước
                </Button>
                <span className="text-sm">
                  Trang {table.getState().pagination.pageIndex + 1} / {pageCount || 1}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => table.nextPage()}
                  disabled={!table.getCanNextPage()}
                >
                  Sau
                </Button>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
} 