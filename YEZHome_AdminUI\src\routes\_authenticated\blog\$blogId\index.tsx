import { createFileRoute } from "@tanstack/react-router";
import { BlogDetailPage } from "@/features/blog-management/components/BlogDetailPage";
import { Loading } from "@/components/ui/loading";

export const Route = createFileRoute("/_authenticated/blog/$blogId/")({
  component: BlogDetailPage,
  beforeLoad: () => {
    return {
      getTitle: () => 'Chi tiết tin tức'
    };
  },
  pendingComponent: () => <Loading />,
  errorComponent: () => <div className="p-10"><PERSON><PERSON> xảy ra lỗi khi tải dữ liệu.</div>,
});