import { useNavigate, useRouter } from "@tanstack/react-router";
import { useQuery } from "@tanstack/react-query";
import { blogService } from "@/services/blog-service";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Pencil, ArrowLeft, User, Calendar, Tag, FileText } from "lucide-react";
import { Route } from "@/routes/_authenticated/blog/$blogId";
import { Loading } from "@/components/ui/loading";
import { Badge } from "@/components/ui/badge";

export function BlogDetailPage() {
  const { blogId } = Route.useParams();
  const navigate = useNavigate();
  const { history } = useRouter();

  const { data: blog, isLoading, error } = useQuery({
    queryKey: ["blog", blogId],
    queryFn: () => blogService.getBlogPost(blogId),
  });

  if (isLoading) {
    return (
      <Loading />
    );
  }

  if (error || !blog) {
    return (
      <div className="text-center py-10">
        <h2 className="text-2xl font-bold text-red-600">Đã xảy ra lỗi</h2>
        <p className="text-gray-600">
          Không thể tải thông tin bài viết. Vui lòng thử lại sau.
        </p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => navigate({ to: "/blog" })}
        >
          <ArrowLeft className="mr-2 h-4 w-4" /> Quay lại
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with back button */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={() => {
            history.go(-1);
          }}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">Chi tiết bài viết</h1>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={blog.status === 'published' ? "success" : "secondary"} className="px-3 py-1 text-sm capitalize">
            {blog.status || 'Nháp'}
          </Badge>
          <Button 
            onClick={() => {
              navigate({
                to: '/blog/$blogId/edit',
                params: { blogId: blogId },
                replace: true
              });
            }}
          >
            <Pencil className="mr-2 h-4 w-4" /> Chỉnh sửa
          </Button>
        </div>
      </div>

      {/* Blog post info */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">{blog.title}</CardTitle>
          <div className="text-sm text-muted-foreground">
            <span>Tác giả: {blog.authorName}</span>
            <span className="mx-2">•</span>
            <span>
              {blog.publishedAt
                ? `Xuất bản: ${new Date(blog.publishedAt).toLocaleDateString('vi-VN', { 
                    day: '2-digit', 
                    month: 'long', 
                    year: 'numeric' 
                  })}`
                : "Chưa xuất bản"}
            </span>
          </div>
        </CardHeader>
        <CardContent>
          {blog.featuredImage && (
            <div className="mb-6">
              <img
                src={blog.featuredImage}
                alt={blog.title}
                className="w-full max-h-80 object-cover rounded-md"
              />
            </div>
          )}
          <div className="prose max-w-none">
            <div dangerouslySetInnerHTML={{ __html: blog.content }} />
          </div>
        </CardContent>
      </Card>

      {/* Blog metadata */}
      {blog.tags && (
        <Card>
          <CardHeader>
            <CardTitle className="text-xl">Thông tin bổ sung</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="font-semibold">Tác giả:</span>
                  <span>{blog.authorName || 'Không có thông tin'}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="font-semibold">Ngày xuất bản:</span>
                  <span>
                    {blog.publishedAt
                      ? new Date(blog.publishedAt).toLocaleDateString('vi-VN')
                      : 'Chưa xuất bản'}
                  </span>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  <span className="font-semibold">Trạng thái:</span>
                  <span className="capitalize">{blog.status || 'Nháp'}</span>
                </div>
                
                <div className="flex items-start gap-2">
                  <Tag className="h-4 w-4 text-muted-foreground mt-1" />
                  <span className="font-semibold">Tags:</span>
                  <div className="flex flex-wrap gap-2">
                    {blog.tags.split(",").map((tag, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-muted rounded-md text-sm"
                      >
                        {tag.trim()}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 