using System;
using System.Collections.Generic;

namespace RealEstate.Application.DTO
{
    public class TransactionSearchCriteriaDto
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Status { get; set; }
        public string? PaymentMethod { get; set; }
        public string? Type { get; set; }
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
        public string? SearchTerm { get; set; } // For searching in description or reference
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 50;
    }
    
    public class TransactionSearchResultDto
    {
        public IEnumerable<WalletTransactionDto> Transactions { get; set; }
        public int TotalCount { get; set; }
        public int TotalPages { get; set; }
        public int CurrentPage { get; set; }
        public decimal TotalAmount { get; set; }
    }
}
