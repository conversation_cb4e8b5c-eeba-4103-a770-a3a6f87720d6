# AWS SES Email Service Setup Guide

## Overview
This document explains how to set up and configure AWS Simple Email Service (SES) v2 for the YEZ Home Real Estate System.

## Files Modified/Created

### 1. AwsSesEmailSender.cs
- **Location**: `RealEstate.Infrastructure/Services/AwsSesEmailSender.cs`
- **Purpose**: Implementation of `IEmailSender` interface using AWS SES v2
- **Features**:
  - Proper error handling and logging
  - Configuration-based email settings
  - Support for HTML email content
  - UTF-8 character encoding

### 2. Configuration Files
- **appsettings.json**: Production AWS configuration
- **appsettings.Development.json**: Development AWS configuration

### 3. Program.cs
- Added AWS SES service registration
- Configured dependency injection for email services

### 4. EmailTestController.cs
- **Location**: `RealEstate.API/Controllers/EmailTestController.cs`
- **Purpose**: Test endpoint to verify email functionality
- **Endpoint**: `POST /api/EmailTest/send-test-email`

## AWS Configuration

### appsettings.json Structure
```json
{
  "AWS": {
    "Profile": "default",
    "Region": "us-east-1",
    "AccessKey": "YOUR_AWS_ACCESS_KEY",
    "SecretKey": "YOUR_AWS_SECRET_KEY",
    "SES": {
      "FromEmail": "<EMAIL>",
      "FromName": "YEZ Home"
    }
  }
}
```

### Configuration Options

#### AWS Credentials (Choose one method):

**Method 1: Configuration File (Development)**
```json
{
  "AWS": {
    "AccessKey": "YOUR_ACCESS_KEY",
    "SecretKey": "YOUR_SECRET_KEY",
    "Region": "us-east-1"
  }
}
```

**Method 2: AWS Profile (Recommended for Production)**
```json
{
  "AWS": {
    "Profile": "production-profile",
    "Region": "us-east-1"
  }
}
```

**Method 3: Environment Variables**
- `AWS_ACCESS_KEY_ID`
- `AWS_SECRET_ACCESS_KEY`
- `AWS_DEFAULT_REGION`

**Method 4: IAM Roles (Best for EC2/ECS deployment)**
- No credentials needed in configuration
- AWS SDK automatically uses instance role

## AWS SES Setup Steps

### 1. Create AWS Account and SES Service
1. Log in to AWS Console
2. Navigate to Simple Email Service (SES)
3. Choose your region (e.g., us-east-1)

### 2. Verify Email Addresses/Domains
**For Development (Sandbox Mode):**
- Verify individual email addresses
- Can only send to verified addresses

**For Production:**
- Request production access
- Verify your domain
- Can send to any email address

### 3. Create IAM User (if using Access Keys)
1. Go to IAM Console
2. Create new user with programmatic access
3. Attach policy: `AmazonSESFullAccess` or create custom policy:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ses:SendEmail",
                "ses:SendRawEmail"
            ],
            "Resource": "*"
        }
    ]
}
```

## Testing the Email Service

### Using the Test Endpoint

**Request:**
```bash
POST /api/EmailTest/send-test-email
Content-Type: application/json

{
  "toEmail": "<EMAIL>",
  "subject": "Test Email",
  "body": "<h1>Hello</h1><p>This is a test email.</p>"
}
```

**Response (Success):**
```json
{
  "message": "Test email sent successfully!",
  "toEmail": "<EMAIL>"
}
```

**Response (Error):**
```json
{
  "message": "Failed to send email",
  "error": "Error details here"
}
```

## Integration with Existing Features

### Password Reset (AuthController)
The email service can be integrated with the existing password reset functionality:

```csharp
[HttpPost("reset-password")]
public async Task<ActionResult> ResetPassword(ForgotPasswordDto resetPasswordDto)
{
    // Generate reset token
    var resetToken = GenerateResetToken();
    
    // Send email
    var emailBody = $@"
        <h2>Password Reset Request</h2>
        <p>Click the link below to reset your password:</p>
        <a href='https://yezhome.com/reset-password?token={resetToken}'>Reset Password</a>
    ";
    
    await _emailSender.SendEmailAsync(
        resetPasswordDto.Email,
        "Password Reset - YEZ Home",
        emailBody
    );
    
    return Ok(new { Message = "Password reset email sent successfully" });
}
```

## Security Considerations

1. **Never commit AWS credentials to version control**
2. **Use IAM roles in production environments**
3. **Implement rate limiting for email endpoints**
4. **Validate email addresses before sending**
5. **Use HTTPS for all email-related endpoints**
6. **Log email activities for audit purposes**

## Troubleshooting

### Common Issues

**1. Authentication Errors**
- Verify AWS credentials are correct
- Check IAM permissions
- Ensure region is correct

**2. Email Not Delivered**
- Check if sender email is verified in SES
- Verify recipient email (in sandbox mode)
- Check AWS SES sending statistics
- Review CloudWatch logs

**3. Rate Limiting**
- AWS SES has sending limits
- Request limit increases if needed
- Implement retry logic with exponential backoff

### Monitoring and Logging

The service includes comprehensive logging:
- Email sending attempts
- Success/failure status
- Error details
- Performance metrics

Monitor these logs to ensure email delivery reliability.

## Production Deployment Checklist

- [ ] Move out of SES sandbox mode
- [ ] Verify domain in SES
- [ ] Set up proper IAM roles
- [ ] Configure monitoring and alerting
- [ ] Test email delivery thoroughly
- [ ] Set up bounce and complaint handling
- [ ] Configure DKIM and SPF records
- [ ] Remove test endpoints from production
