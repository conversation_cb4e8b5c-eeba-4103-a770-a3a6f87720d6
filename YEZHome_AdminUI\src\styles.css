@import 'tailwindcss';

@plugin "tailwindcss-animate";

@custom-variant dark (&:is(.dark *));

body {
  @apply m-0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

button {
  @apply cursor-pointer;
}

code {
  font-family:
    source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
}

:root {
  --background: oklch(1.0000 0 0);
  --foreground: oklch(0.3211 0 0);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.3211 0 0);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.3211 0 0);
  --primary: oklch(0.7038 0.1230 182.5025);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.9670 0.0029 264.5419);
  --secondary-foreground: oklch(0.4461 0.0263 256.8018);
  --muted: oklch(0.9846 0.0017 247.8389);
  --muted-foreground: oklch(0.5510 0.0234 264.3637);
  --accent: oklch(0.7038 0.1230 182.5025);
  --accent-foreground: oklch(1.0000 0 0);
  --destructive: oklch(0.6450 0.2154 16.4393);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.9276 0.0058 264.5313);
  --input: oklch(0.9276 0.0058 264.5313);
  --ring: oklch(0.7038 0.1230 182.5025);
  --chart-1: oklch(0.7038 0.1230 182.5025);
  --chart-2: oklch(0.6002 0.1038 184.7040);
  --chart-3: oklch(0.5109 0.0861 186.3914);
  --chart-4: oklch(0.3925 0.0896 152.5353);
  --chart-5: oklch(0.4479 0.1083 151.3277);
  --sidebar: oklch(0.9846 0.0017 247.8389);
  --sidebar-foreground: oklch(0.3211 0 0);
  --sidebar-primary: oklch(0.7038 0.1230 182.5025);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.5109 0.0861 186.3914);
  --sidebar-accent-foreground: oklch(1.0000 0 0);
  --sidebar-border: oklch(0.9276 0.0058 264.5313);
  --sidebar-ring: oklch(0.7038 0.1230 182.5025);
  --radius: 0.3rem;
  --shadow-2xs: 0 0 0 0 hsl(0 / 0.00);
  --shadow-xs: 0 0 0 0 hsl(0 / 0.00);
  --shadow-sm: 0 0 0 0 hsl(0 / 0.00), 0 1px 2px -1px hsl(0 / 0.00);
  --shadow: 0 0 0 0 hsl(0 / 0.00), 0 1px 2px -1px hsl(0 / 0.00);
  --shadow-md: 0 0 0 0 hsl(0 / 0.00), 0 2px 4px -1px hsl(0 / 0.00);
  --shadow-lg: 0 0 0 0 hsl(0 / 0.00), 0 4px 6px -1px hsl(0 / 0.00);
  --shadow-xl: 0 0 0 0 hsl(0 / 0.00), 0 8px 10px -1px hsl(0 / 0.00);
  --shadow-2xl: 0 0 0 0 hsl(0 / 0.00);
  --tracking-normal: 0;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.2046 0 0);
  --foreground: oklch(0.9219 0 0);
  --card: oklch(0.2686 0 0);
  --card-foreground: oklch(0.9219 0 0);
  --popover: oklch(0.2686 0 0);
  --popover-foreground: oklch(0.9219 0 0);
  --primary: oklch(0.7038 0.1230 182.5025);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.2686 0 0);
  --secondary-foreground: oklch(0.9219 0 0);
  --muted: oklch(0.2686 0 0);
  --muted-foreground: oklch(0.7155 0 0);
  --accent: oklch(0.7038 0.1230 182.5025);
  --accent-foreground: oklch(1.0000 0 0);
  --destructive: oklch(0.6450 0.2154 16.4393);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.3715 0 0);
  --input: oklch(0.3715 0 0);
  --ring: oklch(0.7038 0.1230 182.5025);
  --chart-1: oklch(0.8452 0.1299 164.9782);
  --chart-2: oklch(0.7729 0.1535 163.2231);
  --chart-3: oklch(0.7038 0.1230 182.5025);
  --chart-4: oklch(0.6002 0.1038 184.7040);
  --chart-5: oklch(0.5109 0.0861 186.3914);
  --sidebar: oklch(0.2046 0 0);
  --sidebar-foreground: oklch(0.9219 0 0);
  --sidebar-primary: oklch(0.7038 0.1230 182.5025);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.5109 0.0861 186.3914);
  --sidebar-accent-foreground: oklch(1.0000 0 0);
  --sidebar-border: oklch(0.3715 0 0);
  --sidebar-ring: oklch(0.7038 0.1230 182.5025);
  --radius: 0.3rem;
  --shadow-2xs: 0 0 0 0 hsl(0 / 0.00);
  --shadow-xs: 0 0 0 0 hsl(0 / 0.00);
  --shadow-sm: 0 0 0 0 hsl(0 / 0.00), 0 1px 2px -1px hsl(0 / 0.00);
  --shadow: 0 0 0 0 hsl(0 / 0.00), 0 1px 2px -1px hsl(0 / 0.00);
  --shadow-md: 0 0 0 0 hsl(0 / 0.00), 0 2px 4px -1px hsl(0 / 0.00);
  --shadow-lg: 0 0 0 0 hsl(0 / 0.00), 0 4px 6px -1px hsl(0 / 0.00);
  --shadow-xl: 0 0 0 0 hsl(0 / 0.00), 0 8px 10px -1px hsl(0 / 0.00);
  --shadow-2xl: 0 0 0 0 hsl(0 / 0.00);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);

  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-normal: var(--tracking-normal);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
}

body {
  letter-spacing: var(--tracking-normal);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    letter-spacing:
    var(--tracking-normal);
  }
}
