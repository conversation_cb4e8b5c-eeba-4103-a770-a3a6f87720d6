import { createFileRoute } from "@tanstack/react-router";
import { EmployeePage } from "@/features/employee/EmployeePage";
import { Loading } from "@/components/ui/loading";
import { employeeSearchSchema } from "@/lib/types/employee";

export const Route = createFileRoute('/_authenticated/employee/')({
  validateSearch: employeeSearchSchema,
  component: EmployeePage,
  beforeLoad: () => {
    return {
      getTitle: () => 'Quản lý nhân viên'
    };
  },
  pendingComponent: () => <Loading />,
  errorComponent: () => <div className="p-10"><PERSON><PERSON> xảy ra lỗi khi tải dữ liệu.</div>,
})
