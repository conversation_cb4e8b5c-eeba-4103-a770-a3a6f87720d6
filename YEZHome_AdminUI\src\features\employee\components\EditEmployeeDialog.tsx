import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { <PERSON><PERSON>, DialogContent, DialogHeader, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Spinner } from '@/components/ui/spinner'
import { toast } from '@/components/ui/use-toast'
import userService from '@/services/user-service'
import type { UserDto } from '@/lib/types/user'
import type { UpdateUserRoleDto } from '@/lib/types/role'

interface EditEmployeeDialogProps {
  employee: UserDto | null
  isOpen: boolean
  onClose: () => void
}

export const EditEmployeeDialog = ({ employee, isOpen, onClose }: EditEmployeeDialogProps) => {
  const queryClient = useQueryClient()

  const { data: employeeDetails, isLoading } = useQuery({
    queryKey: ['employee', employee?.id],
    queryFn: () => employee?.id ? userService.getUserById(employee.id) : null,
    enabled: !!employee?.id && isOpen,
  })

  const { data: roles, isLoading: isLoadingRoles } = useQuery({
    queryKey: ['roles'],
    queryFn: () => userService.getRoles(),
    enabled: isOpen,
  })

  const updateStatusMutation = useMutation({
    mutationFn: ({ id, status }: { id: string, status: boolean }) => 
      userService.updateUserStatus(id, { isActive: status }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] })
      queryClient.invalidateQueries({ queryKey: ['employee'] })
      toast({
        title: "Thành công",
        description: "Đã cập nhật trạng thái nhân viên",
        variant: "success",
      })
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: "Không thể cập nhật trạng thái nhân viên. Vui lòng thử lại.",
        variant: "destructive",
      })
      console.error('Error updating employee status:', error)
    }
  })

  const updateRolesMutation = useMutation({
    mutationFn: (data: UpdateUserRoleDto) => userService.updateUserRoles(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] })
      queryClient.invalidateQueries({ queryKey: ['employee'] })
      toast({
        title: "Thành công",
        description: "Đã cập nhật vai trò nhân viên",
        variant: "success",
      })
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: "Không thể cập nhật vai trò nhân viên. Vui lòng thử lại.",
        variant: "destructive",
      })
      console.error('Error updating employee roles:', error)
    }
  })

  const handleStatusUpdate = (status: boolean) => {
    if (employeeDetails) {
      updateStatusMutation.mutate({ id: employeeDetails.id, status })
    }
  }

  const handleRoleUpdate = (roleId: string) => {
    if (employeeDetails && roleId) {
      updateRolesMutation.mutate({
        userId: employeeDetails.id,
        roleIds: [roleId]
      })
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Chỉnh sửa nhân viên</DialogTitle>
        </DialogHeader>
        {isLoading ? (
          <div className="flex justify-center py-8">
            <Spinner />
          </div>
        ) : employeeDetails ? (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm font-medium">Họ tên</h4>
                <p>{employeeDetails.fullName || '-'}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium">Email</h4>
                <p>{employeeDetails.email || '-'}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium">Số điện thoại</h4>
                <p>{employeeDetails.phone || '-'}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium">Loại người dùng</h4>
                <p>{employeeDetails.userType || '-'}</p>
              </div>
            </div>
            
            <div className="border-t pt-4">
              <h4 className="text-sm font-medium mb-2">Trạng thái tài khoản</h4>
              <div className="flex items-center space-x-4">
                <Button 
                  variant={employeeDetails.isActive ? "outline" : "default"}
                  onClick={() => handleStatusUpdate(false)}
                  disabled={!employeeDetails.isActive || updateStatusMutation.isPending}
                >
                  Vô hiệu hóa
                </Button>
                <Button 
                  variant={employeeDetails.isActive ? "default" : "outline"}
                  onClick={() => handleStatusUpdate(true)}
                  disabled={employeeDetails.isActive || updateStatusMutation.isPending}
                >
                  Kích hoạt
                </Button>
              </div>
            </div>

            <div className="border-t pt-4">
              <h4 className="text-sm font-medium mb-2">Vai trò</h4>
              {isLoadingRoles ? (
                <div className="flex items-center py-2">
                  <Spinner className="h-4 w-4 mr-2" />
                  <span className="text-sm">Đang tải vai trò...</span>
                </div>
              ) : roles && roles.length > 0 ? (
                <div className="space-y-4">
                  <select
                    className="w-full p-2 border rounded-md"
                    value={employeeDetails.roleObjects?.[0]?.id || ""}
                    onChange={(e) => handleRoleUpdate(e.target.value)}
                  >
                    <option value="">-- Chọn vai trò --</option>
                    {roles.map((role) => (
                      <option key={role.id} value={role.id}>
                        {role.roleName}
                      </option>
                    ))}
                  </select>
                  {updateRolesMutation.isPending && (
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Spinner className="h-3 w-3 mr-2" />
                      Đang cập nhật vai trò...
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">Không có vai trò nào</p>
              )}
            </div>
            
            <DialogFooter>
              <Button onClick={onClose}>
                Đóng
              </Button>
            </DialogFooter>
          </div>
        ) : (
          <p className="text-center py-4">Không tìm thấy thông tin nhân viên</p>
        )}
      </DialogContent>
    </Dialog>
  )
} 