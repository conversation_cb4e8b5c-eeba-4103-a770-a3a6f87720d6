namespace RealEstate.Application.DTO
{
    public class UserFilterDto
    {
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 50;
        public string? Email { get; set; }
        public string? Name { get; set; }
        public string? Phone { get; set; }
        public string? UserType { get; set; }
        public bool? IsActive { get; set; }
        public string? SortColumn { get; set; } // Example: "FullName", "Email", "CreatedAt"
        public bool SortDescending { get; set; } = false;
    }
}
