import{r as X,j as y,N as D}from"./index-DFXNGaQ4.js";/**
   * table-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function dt(){return{accessor:(e,o)=>typeof e=="function"?{...o,accessorFn:e}:{...o,accessorKey:e},display:e=>e,group:e=>e}}function P(e,o){return typeof e=="function"?e(o):e}function $(e,o){return t=>{o.setState(n=>({...n,[e]:P(t,n[e])}))}}function O(e){return e instanceof Function}function Re(e){return Array.isArray(e)&&e.every(o=>typeof o=="number")}function we(e,o){const t=[],n=r=>{r.forEach(i=>{t.push(i);const l=o(i);l!=null&&l.length&&n(l)})};return n(e),t}function m(e,o,t){let n=[],r;return i=>{let l;t.key&&t.debug&&(l=Date.now());const u=e(i);if(!(u.length!==n.length||u.some((c,S)=>n[S]!==c)))return r;n=u;let g;if(t.key&&t.debug&&(g=Date.now()),r=o(...u),t==null||t.onChange==null||t.onChange(r),t.key&&t.debug&&t!=null&&t.debug()){const c=Math.round((Date.now()-l)*100)/100,S=Math.round((Date.now()-g)*100)/100,d=S/16,s=(f,p)=>{for(f=String(f);f.length<p;)f=" "+f;return f};console.info(`%c⏱ ${s(S,5)} /${s(c,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*d,120))}deg 100% 31%);`,t==null?void 0:t.key)}return r}}function C(e,o,t,n){return{debug:()=>{var r;return(r=e==null?void 0:e.debugAll)!=null?r:e[o]},key:!1,onChange:n}}function ve(e,o,t,n){const r=()=>{var l;return(l=i.getValue())!=null?l:e.options.renderFallbackValue},i={id:`${o.id}_${t.id}`,row:o,column:t,getValue:()=>o.getValue(n),renderValue:r,getContext:m(()=>[e,t,o,i],(l,u,a,g)=>({table:l,column:u,row:a,cell:g,getValue:g.getValue,renderValue:g.renderValue}),C(e.options,"debugCells"))};return e._features.forEach(l=>{l.createCell==null||l.createCell(i,t,o,e)},{}),i}function he(e,o,t,n){var r,i;const u={...e._getDefaultColumnDef(),...o},a=u.accessorKey;let g=(r=(i=u.id)!=null?i:a?typeof String.prototype.replaceAll=="function"?a.replaceAll(".","_"):a.replace(/\./g,"_"):void 0)!=null?r:typeof u.header=="string"?u.header:void 0,c;if(u.accessorFn?c=u.accessorFn:a&&(a.includes(".")?c=d=>{let s=d;for(const p of a.split(".")){var f;s=(f=s)==null?void 0:f[p]}return s}:c=d=>d[u.accessorKey]),!g)throw new Error;let S={id:`${String(g)}`,accessorFn:c,parent:n,depth:t,columnDef:u,columns:[],getFlatColumns:m(()=>[!0],()=>{var d;return[S,...(d=S.columns)==null?void 0:d.flatMap(s=>s.getFlatColumns())]},C(e.options,"debugColumns")),getLeafColumns:m(()=>[e._getOrderColumnsFn()],d=>{var s;if((s=S.columns)!=null&&s.length){let f=S.columns.flatMap(p=>p.getLeafColumns());return d(f)}return[S]},C(e.options,"debugColumns"))};for(const d of e._features)d.createColumn==null||d.createColumn(S,e);return S}const _="debugHeaders";function ie(e,o,t){var n;let i={id:(n=t.id)!=null?n:o.id,column:o,index:t.index,isPlaceholder:!!t.isPlaceholder,placeholderId:t.placeholderId,depth:t.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{const l=[],u=a=>{a.subHeaders&&a.subHeaders.length&&a.subHeaders.map(u),l.push(a)};return u(i),l},getContext:()=>({table:e,header:i,column:o})};return e._features.forEach(l=>{l.createHeader==null||l.createHeader(i,e)}),i}const _e={createTable:e=>{e.getHeaderGroups=m(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(o,t,n,r)=>{var i,l;const u=(i=n==null?void 0:n.map(S=>t.find(d=>d.id===S)).filter(Boolean))!=null?i:[],a=(l=r==null?void 0:r.map(S=>t.find(d=>d.id===S)).filter(Boolean))!=null?l:[],g=t.filter(S=>!(n!=null&&n.includes(S.id))&&!(r!=null&&r.includes(S.id)));return A(o,[...u,...g,...a],e)},C(e.options,_)),e.getCenterHeaderGroups=m(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(o,t,n,r)=>(t=t.filter(i=>!(n!=null&&n.includes(i.id))&&!(r!=null&&r.includes(i.id))),A(o,t,e,"center")),C(e.options,_)),e.getLeftHeaderGroups=m(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(o,t,n)=>{var r;const i=(r=n==null?void 0:n.map(l=>t.find(u=>u.id===l)).filter(Boolean))!=null?r:[];return A(o,i,e,"left")},C(e.options,_)),e.getRightHeaderGroups=m(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(o,t,n)=>{var r;const i=(r=n==null?void 0:n.map(l=>t.find(u=>u.id===l)).filter(Boolean))!=null?r:[];return A(o,i,e,"right")},C(e.options,_)),e.getFooterGroups=m(()=>[e.getHeaderGroups()],o=>[...o].reverse(),C(e.options,_)),e.getLeftFooterGroups=m(()=>[e.getLeftHeaderGroups()],o=>[...o].reverse(),C(e.options,_)),e.getCenterFooterGroups=m(()=>[e.getCenterHeaderGroups()],o=>[...o].reverse(),C(e.options,_)),e.getRightFooterGroups=m(()=>[e.getRightHeaderGroups()],o=>[...o].reverse(),C(e.options,_)),e.getFlatHeaders=m(()=>[e.getHeaderGroups()],o=>o.map(t=>t.headers).flat(),C(e.options,_)),e.getLeftFlatHeaders=m(()=>[e.getLeftHeaderGroups()],o=>o.map(t=>t.headers).flat(),C(e.options,_)),e.getCenterFlatHeaders=m(()=>[e.getCenterHeaderGroups()],o=>o.map(t=>t.headers).flat(),C(e.options,_)),e.getRightFlatHeaders=m(()=>[e.getRightHeaderGroups()],o=>o.map(t=>t.headers).flat(),C(e.options,_)),e.getCenterLeafHeaders=m(()=>[e.getCenterFlatHeaders()],o=>o.filter(t=>{var n;return!((n=t.subHeaders)!=null&&n.length)}),C(e.options,_)),e.getLeftLeafHeaders=m(()=>[e.getLeftFlatHeaders()],o=>o.filter(t=>{var n;return!((n=t.subHeaders)!=null&&n.length)}),C(e.options,_)),e.getRightLeafHeaders=m(()=>[e.getRightFlatHeaders()],o=>o.filter(t=>{var n;return!((n=t.subHeaders)!=null&&n.length)}),C(e.options,_)),e.getLeafHeaders=m(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(o,t,n)=>{var r,i,l,u,a,g;return[...(r=(i=o[0])==null?void 0:i.headers)!=null?r:[],...(l=(u=t[0])==null?void 0:u.headers)!=null?l:[],...(a=(g=n[0])==null?void 0:g.headers)!=null?a:[]].map(c=>c.getLeafHeaders()).flat()},C(e.options,_))}};function A(e,o,t,n){var r,i;let l=0;const u=function(d,s){s===void 0&&(s=1),l=Math.max(l,s),d.filter(f=>f.getIsVisible()).forEach(f=>{var p;(p=f.columns)!=null&&p.length&&u(f.columns,s+1)},0)};u(e);let a=[];const g=(d,s)=>{const f={depth:s,id:[n,`${s}`].filter(Boolean).join("_"),headers:[]},p=[];d.forEach(R=>{const w=[...p].reverse()[0],v=R.column.depth===f.depth;let h,V=!1;if(v&&R.column.parent?h=R.column.parent:(h=R.column,V=!0),w&&(w==null?void 0:w.column)===h)w.subHeaders.push(R);else{const F=ie(t,h,{id:[n,s,h.id,R==null?void 0:R.id].filter(Boolean).join("_"),isPlaceholder:V,placeholderId:V?`${p.filter(E=>E.column===h).length}`:void 0,depth:s,index:p.length});F.subHeaders.push(R),p.push(F)}f.headers.push(R),R.headerGroup=f}),a.push(f),s>0&&g(p,s-1)},c=o.map((d,s)=>ie(t,d,{depth:l,index:s}));g(c,l-1),a.reverse();const S=d=>d.filter(f=>f.column.getIsVisible()).map(f=>{let p=0,R=0,w=[0];f.subHeaders&&f.subHeaders.length?(w=[],S(f.subHeaders).forEach(h=>{let{colSpan:V,rowSpan:F}=h;p+=V,w.push(F)})):p=1;const v=Math.min(...w);return R=R+v,f.colSpan=p,f.rowSpan=R,{colSpan:p,rowSpan:R}});return S((r=(i=a[0])==null?void 0:i.headers)!=null?r:[]),a}const Z=(e,o,t,n,r,i,l)=>{let u={id:o,index:n,original:t,depth:r,parentId:l,_valuesCache:{},_uniqueValuesCache:{},getValue:a=>{if(u._valuesCache.hasOwnProperty(a))return u._valuesCache[a];const g=e.getColumn(a);if(g!=null&&g.accessorFn)return u._valuesCache[a]=g.accessorFn(u.original,n),u._valuesCache[a]},getUniqueValues:a=>{if(u._uniqueValuesCache.hasOwnProperty(a))return u._uniqueValuesCache[a];const g=e.getColumn(a);if(g!=null&&g.accessorFn)return g.columnDef.getUniqueValues?(u._uniqueValuesCache[a]=g.columnDef.getUniqueValues(u.original,n),u._uniqueValuesCache[a]):(u._uniqueValuesCache[a]=[u.getValue(a)],u._uniqueValuesCache[a])},renderValue:a=>{var g;return(g=u.getValue(a))!=null?g:e.options.renderFallbackValue},subRows:[],getLeafRows:()=>we(u.subRows,a=>a.subRows),getParentRow:()=>u.parentId?e.getRow(u.parentId,!0):void 0,getParentRows:()=>{let a=[],g=u;for(;;){const c=g.getParentRow();if(!c)break;a.push(c),g=c}return a.reverse()},getAllCells:m(()=>[e.getAllLeafColumns()],a=>a.map(g=>ve(e,u,g,g.id)),C(e.options,"debugRows")),_getAllCellsByColumnId:m(()=>[u.getAllCells()],a=>a.reduce((g,c)=>(g[c.column.id]=c,g),{}),C(e.options,"debugRows"))};for(let a=0;a<e._features.length;a++){const g=e._features[a];g==null||g.createRow==null||g.createRow(u,e)}return u},Fe={createColumn:(e,o)=>{e._getFacetedRowModel=o.options.getFacetedRowModel&&o.options.getFacetedRowModel(o,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():o.getPreFilteredRowModel(),e._getFacetedUniqueValues=o.options.getFacetedUniqueValues&&o.options.getFacetedUniqueValues(o,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=o.options.getFacetedMinMaxValues&&o.options.getFacetedMinMaxValues(o,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},se=(e,o,t)=>{var n,r;const i=t==null||(n=t.toString())==null?void 0:n.toLowerCase();return!!(!((r=e.getValue(o))==null||(r=r.toString())==null||(r=r.toLowerCase())==null)&&r.includes(i))};se.autoRemove=e=>x(e);const ue=(e,o,t)=>{var n;return!!(!((n=e.getValue(o))==null||(n=n.toString())==null)&&n.includes(t))};ue.autoRemove=e=>x(e);const ae=(e,o,t)=>{var n;return((n=e.getValue(o))==null||(n=n.toString())==null?void 0:n.toLowerCase())===(t==null?void 0:t.toLowerCase())};ae.autoRemove=e=>x(e);const ge=(e,o,t)=>{var n;return(n=e.getValue(o))==null?void 0:n.includes(t)};ge.autoRemove=e=>x(e);const de=(e,o,t)=>!t.some(n=>{var r;return!((r=e.getValue(o))!=null&&r.includes(n))});de.autoRemove=e=>x(e)||!(e!=null&&e.length);const ce=(e,o,t)=>t.some(n=>{var r;return(r=e.getValue(o))==null?void 0:r.includes(n)});ce.autoRemove=e=>x(e)||!(e!=null&&e.length);const fe=(e,o,t)=>e.getValue(o)===t;fe.autoRemove=e=>x(e);const pe=(e,o,t)=>e.getValue(o)==t;pe.autoRemove=e=>x(e);const b=(e,o,t)=>{let[n,r]=t;const i=e.getValue(o);return i>=n&&i<=r};b.resolveFilterValue=e=>{let[o,t]=e,n=typeof o!="number"?parseFloat(o):o,r=typeof t!="number"?parseFloat(t):t,i=o===null||Number.isNaN(n)?-1/0:n,l=t===null||Number.isNaN(r)?1/0:r;if(i>l){const u=i;i=l,l=u}return[i,l]};b.autoRemove=e=>x(e)||x(e[0])&&x(e[1]);const M={includesString:se,includesStringSensitive:ue,equalsString:ae,arrIncludes:ge,arrIncludesAll:de,arrIncludesSome:ce,equals:fe,weakEquals:pe,inNumberRange:b};function x(e){return e==null||e===""}const $e={getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:$("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,o)=>{e.getAutoFilterFn=()=>{const t=o.getCoreRowModel().flatRows[0],n=t==null?void 0:t.getValue(e.id);return typeof n=="string"?M.includesString:typeof n=="number"?M.inNumberRange:typeof n=="boolean"||n!==null&&typeof n=="object"?M.equals:Array.isArray(n)?M.arrIncludes:M.weakEquals},e.getFilterFn=()=>{var t,n;return O(e.columnDef.filterFn)?e.columnDef.filterFn:e.columnDef.filterFn==="auto"?e.getAutoFilterFn():(t=(n=o.options.filterFns)==null?void 0:n[e.columnDef.filterFn])!=null?t:M[e.columnDef.filterFn]},e.getCanFilter=()=>{var t,n,r;return((t=e.columnDef.enableColumnFilter)!=null?t:!0)&&((n=o.options.enableColumnFilters)!=null?n:!0)&&((r=o.options.enableFilters)!=null?r:!0)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var t;return(t=o.getState().columnFilters)==null||(t=t.find(n=>n.id===e.id))==null?void 0:t.value},e.getFilterIndex=()=>{var t,n;return(t=(n=o.getState().columnFilters)==null?void 0:n.findIndex(r=>r.id===e.id))!=null?t:-1},e.setFilterValue=t=>{o.setColumnFilters(n=>{const r=e.getFilterFn(),i=n==null?void 0:n.find(c=>c.id===e.id),l=P(t,i?i.value:void 0);if(le(r,l,e)){var u;return(u=n==null?void 0:n.filter(c=>c.id!==e.id))!=null?u:[]}const a={id:e.id,value:l};if(i){var g;return(g=n==null?void 0:n.map(c=>c.id===e.id?a:c))!=null?g:[]}return n!=null&&n.length?[...n,a]:[a]})}},createRow:(e,o)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=o=>{const t=e.getAllLeafColumns(),n=r=>{var i;return(i=P(o,r))==null?void 0:i.filter(l=>{const u=t.find(a=>a.id===l.id);if(u){const a=u.getFilterFn();if(le(a,l.value,u))return!1}return!0})};e.options.onColumnFiltersChange==null||e.options.onColumnFiltersChange(n)},e.resetColumnFilters=o=>{var t,n;e.setColumnFilters(o?[]:(t=(n=e.initialState)==null?void 0:n.columnFilters)!=null?t:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel?e.getPreFilteredRowModel():e._getFilteredRowModel())}};function le(e,o,t){return(e&&e.autoRemove?e.autoRemove(o,t):!1)||typeof o>"u"||typeof o=="string"&&!o}const xe=(e,o,t)=>t.reduce((n,r)=>{const i=r.getValue(e);return n+(typeof i=="number"?i:0)},0),Ve=(e,o,t)=>{let n;return t.forEach(r=>{const i=r.getValue(e);i!=null&&(n>i||n===void 0&&i>=i)&&(n=i)}),n},Me=(e,o,t)=>{let n;return t.forEach(r=>{const i=r.getValue(e);i!=null&&(n<i||n===void 0&&i>=i)&&(n=i)}),n},Pe=(e,o,t)=>{let n,r;return t.forEach(i=>{const l=i.getValue(e);l!=null&&(n===void 0?l>=l&&(n=r=l):(n>l&&(n=l),r<l&&(r=l)))}),[n,r]},Ie=(e,o)=>{let t=0,n=0;if(o.forEach(r=>{let i=r.getValue(e);i!=null&&(i=+i)>=i&&(++t,n+=i)}),t)return n/t},ye=(e,o)=>{if(!o.length)return;const t=o.map(i=>i.getValue(e));if(!Re(t))return;if(t.length===1)return t[0];const n=Math.floor(t.length/2),r=t.sort((i,l)=>i-l);return t.length%2!==0?r[n]:(r[n-1]+r[n])/2},Ee=(e,o)=>Array.from(new Set(o.map(t=>t.getValue(e))).values()),De=(e,o)=>new Set(o.map(t=>t.getValue(e))).size,Ge=(e,o)=>o.length,B={sum:xe,min:Ve,max:Me,extent:Pe,mean:Ie,median:ye,unique:Ee,uniqueCount:De,count:Ge},He={getDefaultColumnDef:()=>({aggregatedCell:e=>{var o,t;return(o=(t=e.getValue())==null||t.toString==null?void 0:t.toString())!=null?o:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:$("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,o)=>{e.toggleGrouping=()=>{o.setGrouping(t=>t!=null&&t.includes(e.id)?t.filter(n=>n!==e.id):[...t??[],e.id])},e.getCanGroup=()=>{var t,n;return((t=e.columnDef.enableGrouping)!=null?t:!0)&&((n=o.options.enableGrouping)!=null?n:!0)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var t;return(t=o.getState().grouping)==null?void 0:t.includes(e.id)},e.getGroupedIndex=()=>{var t;return(t=o.getState().grouping)==null?void 0:t.indexOf(e.id)},e.getToggleGroupingHandler=()=>{const t=e.getCanGroup();return()=>{t&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{const t=o.getCoreRowModel().flatRows[0],n=t==null?void 0:t.getValue(e.id);if(typeof n=="number")return B.sum;if(Object.prototype.toString.call(n)==="[object Date]")return B.extent},e.getAggregationFn=()=>{var t,n;if(!e)throw new Error;return O(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:e.columnDef.aggregationFn==="auto"?e.getAutoAggregationFn():(t=(n=o.options.aggregationFns)==null?void 0:n[e.columnDef.aggregationFn])!=null?t:B[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=o=>e.options.onGroupingChange==null?void 0:e.options.onGroupingChange(o),e.resetGrouping=o=>{var t,n;e.setGrouping(o?[]:(t=(n=e.initialState)==null?void 0:n.grouping)!=null?t:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel?e.getPreGroupedRowModel():e._getGroupedRowModel())},createRow:(e,o)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=t=>{if(e._groupingValuesCache.hasOwnProperty(t))return e._groupingValuesCache[t];const n=o.getColumn(t);return n!=null&&n.columnDef.getGroupingValue?(e._groupingValuesCache[t]=n.columnDef.getGroupingValue(e.original),e._groupingValuesCache[t]):e.getValue(t)},e._groupingValuesCache={}},createCell:(e,o,t,n)=>{e.getIsGrouped=()=>o.getIsGrouped()&&o.id===t.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&o.getIsGrouped(),e.getIsAggregated=()=>{var r;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!((r=t.subRows)!=null&&r.length)}}};function Ae(e,o,t){if(!(o!=null&&o.length)||!t)return e;const n=e.filter(i=>!o.includes(i.id));return t==="remove"?n:[...o.map(i=>e.find(l=>l.id===i)).filter(Boolean),...n]}const Le={getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:$("columnOrder",e)}),createColumn:(e,o)=>{e.getIndex=m(t=>[H(o,t)],t=>t.findIndex(n=>n.id===e.id),C(o.options,"debugColumns")),e.getIsFirstColumn=t=>{var n;return((n=H(o,t)[0])==null?void 0:n.id)===e.id},e.getIsLastColumn=t=>{var n;const r=H(o,t);return((n=r[r.length-1])==null?void 0:n.id)===e.id}},createTable:e=>{e.setColumnOrder=o=>e.options.onColumnOrderChange==null?void 0:e.options.onColumnOrderChange(o),e.resetColumnOrder=o=>{var t;e.setColumnOrder(o?[]:(t=e.initialState.columnOrder)!=null?t:[])},e._getOrderColumnsFn=m(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(o,t,n)=>r=>{let i=[];if(!(o!=null&&o.length))i=r;else{const l=[...o],u=[...r];for(;u.length&&l.length;){const a=l.shift(),g=u.findIndex(c=>c.id===a);g>-1&&i.push(u.splice(g,1)[0])}i=[...i,...u]}return Ae(i,t,n)},C(e.options,"debugTable"))}},T=()=>({left:[],right:[]}),ze={getInitialState:e=>({columnPinning:T(),...e}),getDefaultOptions:e=>({onColumnPinningChange:$("columnPinning",e)}),createColumn:(e,o)=>{e.pin=t=>{const n=e.getLeafColumns().map(r=>r.id).filter(Boolean);o.setColumnPinning(r=>{var i,l;if(t==="right"){var u,a;return{left:((u=r==null?void 0:r.left)!=null?u:[]).filter(S=>!(n!=null&&n.includes(S))),right:[...((a=r==null?void 0:r.right)!=null?a:[]).filter(S=>!(n!=null&&n.includes(S))),...n]}}if(t==="left"){var g,c;return{left:[...((g=r==null?void 0:r.left)!=null?g:[]).filter(S=>!(n!=null&&n.includes(S))),...n],right:((c=r==null?void 0:r.right)!=null?c:[]).filter(S=>!(n!=null&&n.includes(S)))}}return{left:((i=r==null?void 0:r.left)!=null?i:[]).filter(S=>!(n!=null&&n.includes(S))),right:((l=r==null?void 0:r.right)!=null?l:[]).filter(S=>!(n!=null&&n.includes(S)))}})},e.getCanPin=()=>e.getLeafColumns().some(n=>{var r,i,l;return((r=n.columnDef.enablePinning)!=null?r:!0)&&((i=(l=o.options.enableColumnPinning)!=null?l:o.options.enablePinning)!=null?i:!0)}),e.getIsPinned=()=>{const t=e.getLeafColumns().map(u=>u.id),{left:n,right:r}=o.getState().columnPinning,i=t.some(u=>n==null?void 0:n.includes(u)),l=t.some(u=>r==null?void 0:r.includes(u));return i?"left":l?"right":!1},e.getPinnedIndex=()=>{var t,n;const r=e.getIsPinned();return r?(t=(n=o.getState().columnPinning)==null||(n=n[r])==null?void 0:n.indexOf(e.id))!=null?t:-1:0}},createRow:(e,o)=>{e.getCenterVisibleCells=m(()=>[e._getAllVisibleCells(),o.getState().columnPinning.left,o.getState().columnPinning.right],(t,n,r)=>{const i=[...n??[],...r??[]];return t.filter(l=>!i.includes(l.column.id))},C(o.options,"debugRows")),e.getLeftVisibleCells=m(()=>[e._getAllVisibleCells(),o.getState().columnPinning.left],(t,n)=>(n??[]).map(i=>t.find(l=>l.column.id===i)).filter(Boolean).map(i=>({...i,position:"left"})),C(o.options,"debugRows")),e.getRightVisibleCells=m(()=>[e._getAllVisibleCells(),o.getState().columnPinning.right],(t,n)=>(n??[]).map(i=>t.find(l=>l.column.id===i)).filter(Boolean).map(i=>({...i,position:"right"})),C(o.options,"debugRows"))},createTable:e=>{e.setColumnPinning=o=>e.options.onColumnPinningChange==null?void 0:e.options.onColumnPinningChange(o),e.resetColumnPinning=o=>{var t,n;return e.setColumnPinning(o?T():(t=(n=e.initialState)==null?void 0:n.columnPinning)!=null?t:T())},e.getIsSomeColumnsPinned=o=>{var t;const n=e.getState().columnPinning;if(!o){var r,i;return!!((r=n.left)!=null&&r.length||(i=n.right)!=null&&i.length)}return!!((t=n[o])!=null&&t.length)},e.getLeftLeafColumns=m(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(o,t)=>(t??[]).map(n=>o.find(r=>r.id===n)).filter(Boolean),C(e.options,"debugColumns")),e.getRightLeafColumns=m(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(o,t)=>(t??[]).map(n=>o.find(r=>r.id===n)).filter(Boolean),C(e.options,"debugColumns")),e.getCenterLeafColumns=m(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(o,t,n)=>{const r=[...t??[],...n??[]];return o.filter(i=>!r.includes(i.id))},C(e.options,"debugColumns"))}};function Oe(e){return e||(typeof document<"u"?document:null)}const L={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},q=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),Be={getDefaultColumnDef:()=>L,getInitialState:e=>({columnSizing:{},columnSizingInfo:q(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:$("columnSizing",e),onColumnSizingInfoChange:$("columnSizingInfo",e)}),createColumn:(e,o)=>{e.getSize=()=>{var t,n,r;const i=o.getState().columnSizing[e.id];return Math.min(Math.max((t=e.columnDef.minSize)!=null?t:L.minSize,(n=i??e.columnDef.size)!=null?n:L.size),(r=e.columnDef.maxSize)!=null?r:L.maxSize)},e.getStart=m(t=>[t,H(o,t),o.getState().columnSizing],(t,n)=>n.slice(0,e.getIndex(t)).reduce((r,i)=>r+i.getSize(),0),C(o.options,"debugColumns")),e.getAfter=m(t=>[t,H(o,t),o.getState().columnSizing],(t,n)=>n.slice(e.getIndex(t)+1).reduce((r,i)=>r+i.getSize(),0),C(o.options,"debugColumns")),e.resetSize=()=>{o.setColumnSizing(t=>{let{[e.id]:n,...r}=t;return r})},e.getCanResize=()=>{var t,n;return((t=e.columnDef.enableResizing)!=null?t:!0)&&((n=o.options.enableColumnResizing)!=null?n:!0)},e.getIsResizing=()=>o.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,o)=>{e.getSize=()=>{let t=0;const n=r=>{if(r.subHeaders.length)r.subHeaders.forEach(n);else{var i;t+=(i=r.column.getSize())!=null?i:0}};return n(e),t},e.getStart=()=>{if(e.index>0){const t=e.headerGroup.headers[e.index-1];return t.getStart()+t.getSize()}return 0},e.getResizeHandler=t=>{const n=o.getColumn(e.column.id),r=n==null?void 0:n.getCanResize();return i=>{if(!n||!r||(i.persist==null||i.persist(),k(i)&&i.touches&&i.touches.length>1))return;const l=e.getSize(),u=e?e.getLeafHeaders().map(w=>[w.column.id,w.column.getSize()]):[[n.id,n.getSize()]],a=k(i)?Math.round(i.touches[0].clientX):i.clientX,g={},c=(w,v)=>{typeof v=="number"&&(o.setColumnSizingInfo(h=>{var V,F;const E=o.options.columnResizeDirection==="rtl"?-1:1,ne=(v-((V=h==null?void 0:h.startOffset)!=null?V:0))*E,oe=Math.max(ne/((F=h==null?void 0:h.startSize)!=null?F:0),-.999999);return h.columnSizingStart.forEach(me=>{let[Ce,re]=me;g[Ce]=Math.round(Math.max(re+re*oe,0)*100)/100}),{...h,deltaOffset:ne,deltaPercentage:oe}}),(o.options.columnResizeMode==="onChange"||w==="end")&&o.setColumnSizing(h=>({...h,...g})))},S=w=>c("move",w),d=w=>{c("end",w),o.setColumnSizingInfo(v=>({...v,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},s=Oe(t),f={moveHandler:w=>S(w.clientX),upHandler:w=>{s==null||s.removeEventListener("mousemove",f.moveHandler),s==null||s.removeEventListener("mouseup",f.upHandler),d(w.clientX)}},p={moveHandler:w=>(w.cancelable&&(w.preventDefault(),w.stopPropagation()),S(w.touches[0].clientX),!1),upHandler:w=>{var v;s==null||s.removeEventListener("touchmove",p.moveHandler),s==null||s.removeEventListener("touchend",p.upHandler),w.cancelable&&(w.preventDefault(),w.stopPropagation()),d((v=w.touches[0])==null?void 0:v.clientX)}},R=Te()?{passive:!1}:!1;k(i)?(s==null||s.addEventListener("touchmove",p.moveHandler,R),s==null||s.addEventListener("touchend",p.upHandler,R)):(s==null||s.addEventListener("mousemove",f.moveHandler,R),s==null||s.addEventListener("mouseup",f.upHandler,R)),o.setColumnSizingInfo(w=>({...w,startOffset:a,startSize:l,deltaOffset:0,deltaPercentage:0,columnSizingStart:u,isResizingColumn:n.id}))}}},createTable:e=>{e.setColumnSizing=o=>e.options.onColumnSizingChange==null?void 0:e.options.onColumnSizingChange(o),e.setColumnSizingInfo=o=>e.options.onColumnSizingInfoChange==null?void 0:e.options.onColumnSizingInfoChange(o),e.resetColumnSizing=o=>{var t;e.setColumnSizing(o?{}:(t=e.initialState.columnSizing)!=null?t:{})},e.resetHeaderSizeInfo=o=>{var t;e.setColumnSizingInfo(o?q():(t=e.initialState.columnSizingInfo)!=null?t:q())},e.getTotalSize=()=>{var o,t;return(o=(t=e.getHeaderGroups()[0])==null?void 0:t.headers.reduce((n,r)=>n+r.getSize(),0))!=null?o:0},e.getLeftTotalSize=()=>{var o,t;return(o=(t=e.getLeftHeaderGroups()[0])==null?void 0:t.headers.reduce((n,r)=>n+r.getSize(),0))!=null?o:0},e.getCenterTotalSize=()=>{var o,t;return(o=(t=e.getCenterHeaderGroups()[0])==null?void 0:t.headers.reduce((n,r)=>n+r.getSize(),0))!=null?o:0},e.getRightTotalSize=()=>{var o,t;return(o=(t=e.getRightHeaderGroups()[0])==null?void 0:t.headers.reduce((n,r)=>n+r.getSize(),0))!=null?o:0}}};let z=null;function Te(){if(typeof z=="boolean")return z;let e=!1;try{const o={get passive(){return e=!0,!1}},t=()=>{};window.addEventListener("test",t,o),window.removeEventListener("test",t)}catch{e=!1}return z=e,z}function k(e){return e.type==="touchstart"}const qe={getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:$("columnVisibility",e)}),createColumn:(e,o)=>{e.toggleVisibility=t=>{e.getCanHide()&&o.setColumnVisibility(n=>({...n,[e.id]:t??!e.getIsVisible()}))},e.getIsVisible=()=>{var t,n;const r=e.columns;return(t=r.length?r.some(i=>i.getIsVisible()):(n=o.getState().columnVisibility)==null?void 0:n[e.id])!=null?t:!0},e.getCanHide=()=>{var t,n;return((t=e.columnDef.enableHiding)!=null?t:!0)&&((n=o.options.enableHiding)!=null?n:!0)},e.getToggleVisibilityHandler=()=>t=>{e.toggleVisibility==null||e.toggleVisibility(t.target.checked)}},createRow:(e,o)=>{e._getAllVisibleCells=m(()=>[e.getAllCells(),o.getState().columnVisibility],t=>t.filter(n=>n.column.getIsVisible()),C(o.options,"debugRows")),e.getVisibleCells=m(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(t,n,r)=>[...t,...n,...r],C(o.options,"debugRows"))},createTable:e=>{const o=(t,n)=>m(()=>[n(),n().filter(r=>r.getIsVisible()).map(r=>r.id).join("_")],r=>r.filter(i=>i.getIsVisible==null?void 0:i.getIsVisible()),C(e.options,"debugColumns"));e.getVisibleFlatColumns=o("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=o("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=o("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=o("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=o("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=t=>e.options.onColumnVisibilityChange==null?void 0:e.options.onColumnVisibilityChange(t),e.resetColumnVisibility=t=>{var n;e.setColumnVisibility(t?{}:(n=e.initialState.columnVisibility)!=null?n:{})},e.toggleAllColumnsVisible=t=>{var n;t=(n=t)!=null?n:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((r,i)=>({...r,[i.id]:t||!(i.getCanHide!=null&&i.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(t=>!(t.getIsVisible!=null&&t.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(t=>t.getIsVisible==null?void 0:t.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>t=>{var n;e.toggleAllColumnsVisible((n=t.target)==null?void 0:n.checked)}}};function H(e,o){return o?o==="center"?e.getCenterVisibleLeafColumns():o==="left"?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}const ke={createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},je={getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:$("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:o=>{var t;const n=(t=e.getCoreRowModel().flatRows[0])==null||(t=t._getAllCellsByColumnId()[o.id])==null?void 0:t.getValue();return typeof n=="string"||typeof n=="number"}}),createColumn:(e,o)=>{e.getCanGlobalFilter=()=>{var t,n,r,i;return((t=e.columnDef.enableGlobalFilter)!=null?t:!0)&&((n=o.options.enableGlobalFilter)!=null?n:!0)&&((r=o.options.enableFilters)!=null?r:!0)&&((i=o.options.getColumnCanGlobalFilter==null?void 0:o.options.getColumnCanGlobalFilter(e))!=null?i:!0)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>M.includesString,e.getGlobalFilterFn=()=>{var o,t;const{globalFilterFn:n}=e.options;return O(n)?n:n==="auto"?e.getGlobalAutoFilterFn():(o=(t=e.options.filterFns)==null?void 0:t[n])!=null?o:M[n]},e.setGlobalFilter=o=>{e.options.onGlobalFilterChange==null||e.options.onGlobalFilterChange(o)},e.resetGlobalFilter=o=>{e.setGlobalFilter(o?void 0:e.initialState.globalFilter)}}},Ne={getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:$("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let o=!1,t=!1;e._autoResetExpanded=()=>{var n,r;if(!o){e._queue(()=>{o=!0});return}if((n=(r=e.options.autoResetAll)!=null?r:e.options.autoResetExpanded)!=null?n:!e.options.manualExpanding){if(t)return;t=!0,e._queue(()=>{e.resetExpanded(),t=!1})}},e.setExpanded=n=>e.options.onExpandedChange==null?void 0:e.options.onExpandedChange(n),e.toggleAllRowsExpanded=n=>{n??!e.getIsAllRowsExpanded()?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=n=>{var r,i;e.setExpanded(n?{}:(r=(i=e.initialState)==null?void 0:i.expanded)!=null?r:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(n=>n.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>n=>{n.persist==null||n.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{const n=e.getState().expanded;return n===!0||Object.values(n).some(Boolean)},e.getIsAllRowsExpanded=()=>{const n=e.getState().expanded;return typeof n=="boolean"?n===!0:!(!Object.keys(n).length||e.getRowModel().flatRows.some(r=>!r.getIsExpanded()))},e.getExpandedDepth=()=>{let n=0;return(e.getState().expanded===!0?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(i=>{const l=i.split(".");n=Math.max(n,l.length)}),n},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel?e.getPreExpandedRowModel():e._getExpandedRowModel())},createRow:(e,o)=>{e.toggleExpanded=t=>{o.setExpanded(n=>{var r;const i=n===!0?!0:!!(n!=null&&n[e.id]);let l={};if(n===!0?Object.keys(o.getRowModel().rowsById).forEach(u=>{l[u]=!0}):l=n,t=(r=t)!=null?r:!i,!i&&t)return{...l,[e.id]:!0};if(i&&!t){const{[e.id]:u,...a}=l;return a}return n})},e.getIsExpanded=()=>{var t;const n=o.getState().expanded;return!!((t=o.options.getIsRowExpanded==null?void 0:o.options.getIsRowExpanded(e))!=null?t:n===!0||n!=null&&n[e.id])},e.getCanExpand=()=>{var t,n,r;return(t=o.options.getRowCanExpand==null?void 0:o.options.getRowCanExpand(e))!=null?t:((n=o.options.enableExpanding)!=null?n:!0)&&!!((r=e.subRows)!=null&&r.length)},e.getIsAllParentsExpanded=()=>{let t=!0,n=e;for(;t&&n.parentId;)n=o.getRow(n.parentId,!0),t=n.getIsExpanded();return t},e.getToggleExpandedHandler=()=>{const t=e.getCanExpand();return()=>{t&&e.toggleExpanded()}}}},K=0,J=10,j=()=>({pageIndex:K,pageSize:J}),Ue={getInitialState:e=>({...e,pagination:{...j(),...e==null?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:$("pagination",e)}),createTable:e=>{let o=!1,t=!1;e._autoResetPageIndex=()=>{var n,r;if(!o){e._queue(()=>{o=!0});return}if((n=(r=e.options.autoResetAll)!=null?r:e.options.autoResetPageIndex)!=null?n:!e.options.manualPagination){if(t)return;t=!0,e._queue(()=>{e.resetPageIndex(),t=!1})}},e.setPagination=n=>{const r=i=>P(n,i);return e.options.onPaginationChange==null?void 0:e.options.onPaginationChange(r)},e.resetPagination=n=>{var r;e.setPagination(n?j():(r=e.initialState.pagination)!=null?r:j())},e.setPageIndex=n=>{e.setPagination(r=>{let i=P(n,r.pageIndex);const l=typeof e.options.pageCount>"u"||e.options.pageCount===-1?Number.MAX_SAFE_INTEGER:e.options.pageCount-1;return i=Math.max(0,Math.min(i,l)),{...r,pageIndex:i}})},e.resetPageIndex=n=>{var r,i;e.setPageIndex(n?K:(r=(i=e.initialState)==null||(i=i.pagination)==null?void 0:i.pageIndex)!=null?r:K)},e.resetPageSize=n=>{var r,i;e.setPageSize(n?J:(r=(i=e.initialState)==null||(i=i.pagination)==null?void 0:i.pageSize)!=null?r:J)},e.setPageSize=n=>{e.setPagination(r=>{const i=Math.max(1,P(n,r.pageSize)),l=r.pageSize*r.pageIndex,u=Math.floor(l/i);return{...r,pageIndex:u,pageSize:i}})},e.setPageCount=n=>e.setPagination(r=>{var i;let l=P(n,(i=e.options.pageCount)!=null?i:-1);return typeof l=="number"&&(l=Math.max(-1,l)),{...r,pageCount:l}}),e.getPageOptions=m(()=>[e.getPageCount()],n=>{let r=[];return n&&n>0&&(r=[...new Array(n)].fill(null).map((i,l)=>l)),r},C(e.options,"debugTable")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{const{pageIndex:n}=e.getState().pagination,r=e.getPageCount();return r===-1?!0:r===0?!1:n<r-1},e.previousPage=()=>e.setPageIndex(n=>n-1),e.nextPage=()=>e.setPageIndex(n=>n+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel?e.getPrePaginationRowModel():e._getPaginationRowModel()),e.getPageCount=()=>{var n;return(n=e.options.pageCount)!=null?n:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var n;return(n=e.options.rowCount)!=null?n:e.getPrePaginationRowModel().rows.length}}},N=()=>({top:[],bottom:[]}),Xe={getInitialState:e=>({rowPinning:N(),...e}),getDefaultOptions:e=>({onRowPinningChange:$("rowPinning",e)}),createRow:(e,o)=>{e.pin=(t,n,r)=>{const i=n?e.getLeafRows().map(a=>{let{id:g}=a;return g}):[],l=r?e.getParentRows().map(a=>{let{id:g}=a;return g}):[],u=new Set([...l,e.id,...i]);o.setRowPinning(a=>{var g,c;if(t==="bottom"){var S,d;return{top:((S=a==null?void 0:a.top)!=null?S:[]).filter(p=>!(u!=null&&u.has(p))),bottom:[...((d=a==null?void 0:a.bottom)!=null?d:[]).filter(p=>!(u!=null&&u.has(p))),...Array.from(u)]}}if(t==="top"){var s,f;return{top:[...((s=a==null?void 0:a.top)!=null?s:[]).filter(p=>!(u!=null&&u.has(p))),...Array.from(u)],bottom:((f=a==null?void 0:a.bottom)!=null?f:[]).filter(p=>!(u!=null&&u.has(p)))}}return{top:((g=a==null?void 0:a.top)!=null?g:[]).filter(p=>!(u!=null&&u.has(p))),bottom:((c=a==null?void 0:a.bottom)!=null?c:[]).filter(p=>!(u!=null&&u.has(p)))}})},e.getCanPin=()=>{var t;const{enableRowPinning:n,enablePinning:r}=o.options;return typeof n=="function"?n(e):(t=n??r)!=null?t:!0},e.getIsPinned=()=>{const t=[e.id],{top:n,bottom:r}=o.getState().rowPinning,i=t.some(u=>n==null?void 0:n.includes(u)),l=t.some(u=>r==null?void 0:r.includes(u));return i?"top":l?"bottom":!1},e.getPinnedIndex=()=>{var t,n;const r=e.getIsPinned();if(!r)return-1;const i=(t=r==="top"?o.getTopRows():o.getBottomRows())==null?void 0:t.map(l=>{let{id:u}=l;return u});return(n=i==null?void 0:i.indexOf(e.id))!=null?n:-1}},createTable:e=>{e.setRowPinning=o=>e.options.onRowPinningChange==null?void 0:e.options.onRowPinningChange(o),e.resetRowPinning=o=>{var t,n;return e.setRowPinning(o?N():(t=(n=e.initialState)==null?void 0:n.rowPinning)!=null?t:N())},e.getIsSomeRowsPinned=o=>{var t;const n=e.getState().rowPinning;if(!o){var r,i;return!!((r=n.top)!=null&&r.length||(i=n.bottom)!=null&&i.length)}return!!((t=n[o])!=null&&t.length)},e._getPinnedRows=(o,t,n)=>{var r;return((r=e.options.keepPinnedRows)==null||r?(t??[]).map(l=>{const u=e.getRow(l,!0);return u.getIsAllParentsExpanded()?u:null}):(t??[]).map(l=>o.find(u=>u.id===l))).filter(Boolean).map(l=>({...l,position:n}))},e.getTopRows=m(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(o,t)=>e._getPinnedRows(o,t,"top"),C(e.options,"debugRows")),e.getBottomRows=m(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(o,t)=>e._getPinnedRows(o,t,"bottom"),C(e.options,"debugRows")),e.getCenterRows=m(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(o,t,n)=>{const r=new Set([...t??[],...n??[]]);return o.filter(i=>!r.has(i.id))},C(e.options,"debugRows"))}},Ke={getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:$("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=o=>e.options.onRowSelectionChange==null?void 0:e.options.onRowSelectionChange(o),e.resetRowSelection=o=>{var t;return e.setRowSelection(o?{}:(t=e.initialState.rowSelection)!=null?t:{})},e.toggleAllRowsSelected=o=>{e.setRowSelection(t=>{o=typeof o<"u"?o:!e.getIsAllRowsSelected();const n={...t},r=e.getPreGroupedRowModel().flatRows;return o?r.forEach(i=>{i.getCanSelect()&&(n[i.id]=!0)}):r.forEach(i=>{delete n[i.id]}),n})},e.toggleAllPageRowsSelected=o=>e.setRowSelection(t=>{const n=typeof o<"u"?o:!e.getIsAllPageRowsSelected(),r={...t};return e.getRowModel().rows.forEach(i=>{Q(r,i.id,n,!0,e)}),r}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=m(()=>[e.getState().rowSelection,e.getCoreRowModel()],(o,t)=>Object.keys(o).length?U(e,t):{rows:[],flatRows:[],rowsById:{}},C(e.options,"debugTable")),e.getFilteredSelectedRowModel=m(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(o,t)=>Object.keys(o).length?U(e,t):{rows:[],flatRows:[],rowsById:{}},C(e.options,"debugTable")),e.getGroupedSelectedRowModel=m(()=>[e.getState().rowSelection,e.getSortedRowModel()],(o,t)=>Object.keys(o).length?U(e,t):{rows:[],flatRows:[],rowsById:{}},C(e.options,"debugTable")),e.getIsAllRowsSelected=()=>{const o=e.getFilteredRowModel().flatRows,{rowSelection:t}=e.getState();let n=!!(o.length&&Object.keys(t).length);return n&&o.some(r=>r.getCanSelect()&&!t[r.id])&&(n=!1),n},e.getIsAllPageRowsSelected=()=>{const o=e.getPaginationRowModel().flatRows.filter(r=>r.getCanSelect()),{rowSelection:t}=e.getState();let n=!!o.length;return n&&o.some(r=>!t[r.id])&&(n=!1),n},e.getIsSomeRowsSelected=()=>{var o;const t=Object.keys((o=e.getState().rowSelection)!=null?o:{}).length;return t>0&&t<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{const o=e.getPaginationRowModel().flatRows;return e.getIsAllPageRowsSelected()?!1:o.filter(t=>t.getCanSelect()).some(t=>t.getIsSelected()||t.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>o=>{e.toggleAllRowsSelected(o.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>o=>{e.toggleAllPageRowsSelected(o.target.checked)}},createRow:(e,o)=>{e.toggleSelected=(t,n)=>{const r=e.getIsSelected();o.setRowSelection(i=>{var l;if(t=typeof t<"u"?t:!r,e.getCanSelect()&&r===t)return i;const u={...i};return Q(u,e.id,t,(l=n==null?void 0:n.selectChildren)!=null?l:!0,o),u})},e.getIsSelected=()=>{const{rowSelection:t}=o.getState();return ee(e,t)},e.getIsSomeSelected=()=>{const{rowSelection:t}=o.getState();return W(e,t)==="some"},e.getIsAllSubRowsSelected=()=>{const{rowSelection:t}=o.getState();return W(e,t)==="all"},e.getCanSelect=()=>{var t;return typeof o.options.enableRowSelection=="function"?o.options.enableRowSelection(e):(t=o.options.enableRowSelection)!=null?t:!0},e.getCanSelectSubRows=()=>{var t;return typeof o.options.enableSubRowSelection=="function"?o.options.enableSubRowSelection(e):(t=o.options.enableSubRowSelection)!=null?t:!0},e.getCanMultiSelect=()=>{var t;return typeof o.options.enableMultiRowSelection=="function"?o.options.enableMultiRowSelection(e):(t=o.options.enableMultiRowSelection)!=null?t:!0},e.getToggleSelectedHandler=()=>{const t=e.getCanSelect();return n=>{var r;t&&e.toggleSelected((r=n.target)==null?void 0:r.checked)}}}},Q=(e,o,t,n,r)=>{var i;const l=r.getRow(o,!0);t?(l.getCanMultiSelect()||Object.keys(e).forEach(u=>delete e[u]),l.getCanSelect()&&(e[o]=!0)):delete e[o],n&&(i=l.subRows)!=null&&i.length&&l.getCanSelectSubRows()&&l.subRows.forEach(u=>Q(e,u.id,t,n,r))};function U(e,o){const t=e.getState().rowSelection,n=[],r={},i=function(l,u){return l.map(a=>{var g;const c=ee(a,t);if(c&&(n.push(a),r[a.id]=a),(g=a.subRows)!=null&&g.length&&(a={...a,subRows:i(a.subRows)}),c)return a}).filter(Boolean)};return{rows:i(o.rows),flatRows:n,rowsById:r}}function ee(e,o){var t;return(t=o[e.id])!=null?t:!1}function W(e,o,t){var n;if(!((n=e.subRows)!=null&&n.length))return!1;let r=!0,i=!1;return e.subRows.forEach(l=>{if(!(i&&!r)&&(l.getCanSelect()&&(ee(l,o)?i=!0:r=!1),l.subRows&&l.subRows.length)){const u=W(l,o);u==="all"?i=!0:(u==="some"&&(i=!0),r=!1)}}),r?"all":i?"some":!1}const Y=/([0-9]+)/gm,Je=(e,o,t)=>Se(I(e.getValue(t)).toLowerCase(),I(o.getValue(t)).toLowerCase()),Qe=(e,o,t)=>Se(I(e.getValue(t)),I(o.getValue(t))),We=(e,o,t)=>te(I(e.getValue(t)).toLowerCase(),I(o.getValue(t)).toLowerCase()),Ye=(e,o,t)=>te(I(e.getValue(t)),I(o.getValue(t))),Ze=(e,o,t)=>{const n=e.getValue(t),r=o.getValue(t);return n>r?1:n<r?-1:0},be=(e,o,t)=>te(e.getValue(t),o.getValue(t));function te(e,o){return e===o?0:e>o?1:-1}function I(e){return typeof e=="number"?isNaN(e)||e===1/0||e===-1/0?"":String(e):typeof e=="string"?e:""}function Se(e,o){const t=e.split(Y).filter(Boolean),n=o.split(Y).filter(Boolean);for(;t.length&&n.length;){const r=t.shift(),i=n.shift(),l=parseInt(r,10),u=parseInt(i,10),a=[l,u].sort();if(isNaN(a[0])){if(r>i)return 1;if(i>r)return-1;continue}if(isNaN(a[1]))return isNaN(l)?-1:1;if(l>u)return 1;if(u>l)return-1}return t.length-n.length}const G={alphanumeric:Je,alphanumericCaseSensitive:Qe,text:We,textCaseSensitive:Ye,datetime:Ze,basic:be},et={getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:$("sorting",e),isMultiSortEvent:o=>o.shiftKey}),createColumn:(e,o)=>{e.getAutoSortingFn=()=>{const t=o.getFilteredRowModel().flatRows.slice(10);let n=!1;for(const r of t){const i=r==null?void 0:r.getValue(e.id);if(Object.prototype.toString.call(i)==="[object Date]")return G.datetime;if(typeof i=="string"&&(n=!0,i.split(Y).length>1))return G.alphanumeric}return n?G.text:G.basic},e.getAutoSortDir=()=>{const t=o.getFilteredRowModel().flatRows[0];return typeof(t==null?void 0:t.getValue(e.id))=="string"?"asc":"desc"},e.getSortingFn=()=>{var t,n;if(!e)throw new Error;return O(e.columnDef.sortingFn)?e.columnDef.sortingFn:e.columnDef.sortingFn==="auto"?e.getAutoSortingFn():(t=(n=o.options.sortingFns)==null?void 0:n[e.columnDef.sortingFn])!=null?t:G[e.columnDef.sortingFn]},e.toggleSorting=(t,n)=>{const r=e.getNextSortingOrder(),i=typeof t<"u"&&t!==null;o.setSorting(l=>{const u=l==null?void 0:l.find(s=>s.id===e.id),a=l==null?void 0:l.findIndex(s=>s.id===e.id);let g=[],c,S=i?t:r==="desc";if(l!=null&&l.length&&e.getCanMultiSort()&&n?u?c="toggle":c="add":l!=null&&l.length&&a!==l.length-1?c="replace":u?c="toggle":c="replace",c==="toggle"&&(i||r||(c="remove")),c==="add"){var d;g=[...l,{id:e.id,desc:S}],g.splice(0,g.length-((d=o.options.maxMultiSortColCount)!=null?d:Number.MAX_SAFE_INTEGER))}else c==="toggle"?g=l.map(s=>s.id===e.id?{...s,desc:S}:s):c==="remove"?g=l.filter(s=>s.id!==e.id):g=[{id:e.id,desc:S}];return g})},e.getFirstSortDir=()=>{var t,n;return((t=(n=e.columnDef.sortDescFirst)!=null?n:o.options.sortDescFirst)!=null?t:e.getAutoSortDir()==="desc")?"desc":"asc"},e.getNextSortingOrder=t=>{var n,r;const i=e.getFirstSortDir(),l=e.getIsSorted();return l?l!==i&&((n=o.options.enableSortingRemoval)==null||n)&&(!(t&&(r=o.options.enableMultiRemove)!=null)||r)?!1:l==="desc"?"asc":"desc":i},e.getCanSort=()=>{var t,n;return((t=e.columnDef.enableSorting)!=null?t:!0)&&((n=o.options.enableSorting)!=null?n:!0)&&!!e.accessorFn},e.getCanMultiSort=()=>{var t,n;return(t=(n=e.columnDef.enableMultiSort)!=null?n:o.options.enableMultiSort)!=null?t:!!e.accessorFn},e.getIsSorted=()=>{var t;const n=(t=o.getState().sorting)==null?void 0:t.find(r=>r.id===e.id);return n?n.desc?"desc":"asc":!1},e.getSortIndex=()=>{var t,n;return(t=(n=o.getState().sorting)==null?void 0:n.findIndex(r=>r.id===e.id))!=null?t:-1},e.clearSorting=()=>{o.setSorting(t=>t!=null&&t.length?t.filter(n=>n.id!==e.id):[])},e.getToggleSortingHandler=()=>{const t=e.getCanSort();return n=>{t&&(n.persist==null||n.persist(),e.toggleSorting==null||e.toggleSorting(void 0,e.getCanMultiSort()?o.options.isMultiSortEvent==null?void 0:o.options.isMultiSortEvent(n):!1))}}},createTable:e=>{e.setSorting=o=>e.options.onSortingChange==null?void 0:e.options.onSortingChange(o),e.resetSorting=o=>{var t,n;e.setSorting(o?[]:(t=(n=e.initialState)==null?void 0:n.sorting)!=null?t:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel?e.getPreSortedRowModel():e._getSortedRowModel())}},tt=[_e,qe,Le,ze,Fe,$e,ke,je,et,He,Ne,Ue,Xe,Ke,Be];function nt(e){var o,t;const n=[...tt,...(o=e._features)!=null?o:[]];let r={_features:n};const i=r._features.reduce((d,s)=>Object.assign(d,s.getDefaultOptions==null?void 0:s.getDefaultOptions(r)),{}),l=d=>r.options.mergeOptions?r.options.mergeOptions(i,d):{...i,...d};let a={...{},...(t=e.initialState)!=null?t:{}};r._features.forEach(d=>{var s;a=(s=d.getInitialState==null?void 0:d.getInitialState(a))!=null?s:a});const g=[];let c=!1;const S={_features:n,options:{...i,...e},initialState:a,_queue:d=>{g.push(d),c||(c=!0,Promise.resolve().then(()=>{for(;g.length;)g.shift()();c=!1}).catch(s=>setTimeout(()=>{throw s})))},reset:()=>{r.setState(r.initialState)},setOptions:d=>{const s=P(d,r.options);r.options=l(s)},getState:()=>r.options.state,setState:d=>{r.options.onStateChange==null||r.options.onStateChange(d)},_getRowId:(d,s,f)=>{var p;return(p=r.options.getRowId==null?void 0:r.options.getRowId(d,s,f))!=null?p:`${f?[f.id,s].join("."):s}`},getCoreRowModel:()=>(r._getCoreRowModel||(r._getCoreRowModel=r.options.getCoreRowModel(r)),r._getCoreRowModel()),getRowModel:()=>r.getPaginationRowModel(),getRow:(d,s)=>{let f=(s?r.getPrePaginationRowModel():r.getRowModel()).rowsById[d];if(!f&&(f=r.getCoreRowModel().rowsById[d],!f))throw new Error;return f},_getDefaultColumnDef:m(()=>[r.options.defaultColumn],d=>{var s;return d=(s=d)!=null?s:{},{header:f=>{const p=f.header.column.columnDef;return p.accessorKey?p.accessorKey:p.accessorFn?p.id:null},cell:f=>{var p,R;return(p=(R=f.renderValue())==null||R.toString==null?void 0:R.toString())!=null?p:null},...r._features.reduce((f,p)=>Object.assign(f,p.getDefaultColumnDef==null?void 0:p.getDefaultColumnDef()),{}),...d}},C(e,"debugColumns")),_getColumnDefs:()=>r.options.columns,getAllColumns:m(()=>[r._getColumnDefs()],d=>{const s=function(f,p,R){return R===void 0&&(R=0),f.map(w=>{const v=he(r,w,R,p),h=w;return v.columns=h.columns?s(h.columns,v,R+1):[],v})};return s(d)},C(e,"debugColumns")),getAllFlatColumns:m(()=>[r.getAllColumns()],d=>d.flatMap(s=>s.getFlatColumns()),C(e,"debugColumns")),_getAllFlatColumnsById:m(()=>[r.getAllFlatColumns()],d=>d.reduce((s,f)=>(s[f.id]=f,s),{}),C(e,"debugColumns")),getAllLeafColumns:m(()=>[r.getAllColumns(),r._getOrderColumnsFn()],(d,s)=>{let f=d.flatMap(p=>p.getLeafColumns());return s(f)},C(e,"debugColumns")),getColumn:d=>r._getAllFlatColumnsById()[d]};Object.assign(r,S);for(let d=0;d<r._features.length;d++){const s=r._features[d];s==null||s.createTable==null||s.createTable(r)}return r}function ct(){return e=>m(()=>[e.options.data],o=>{const t={rows:[],flatRows:[],rowsById:{}},n=function(r,i,l){i===void 0&&(i=0);const u=[];for(let g=0;g<r.length;g++){const c=Z(e,e._getRowId(r[g],g,l),r[g],g,i,void 0,l==null?void 0:l.id);if(t.flatRows.push(c),t.rowsById[c.id]=c,u.push(c),e.options.getSubRows){var a;c.originalSubRows=e.options.getSubRows(r[g],g),(a=c.originalSubRows)!=null&&a.length&&(c.subRows=n(c.originalSubRows,i+1,c))}}return u};return t.rows=n(o),t},C(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex()))}function ot(e){const o=[],t=n=>{var r;o.push(n),(r=n.subRows)!=null&&r.length&&n.getIsExpanded()&&n.subRows.forEach(t)};return e.rows.forEach(t),{rows:o,flatRows:e.flatRows,rowsById:e.rowsById}}function rt(e,o,t){return t.options.filterFromLeafRows?it(e,o,t):lt(e,o,t)}function it(e,o,t){var n;const r=[],i={},l=(n=t.options.maxLeafRowFilterDepth)!=null?n:100,u=function(a,g){g===void 0&&(g=0);const c=[];for(let d=0;d<a.length;d++){var S;let s=a[d];const f=Z(t,s.id,s.original,s.index,s.depth,void 0,s.parentId);if(f.columnFilters=s.columnFilters,(S=s.subRows)!=null&&S.length&&g<l){if(f.subRows=u(s.subRows,g+1),s=f,o(s)&&!f.subRows.length){c.push(s),i[s.id]=s,r.push(s);continue}if(o(s)||f.subRows.length){c.push(s),i[s.id]=s,r.push(s);continue}}else s=f,o(s)&&(c.push(s),i[s.id]=s,r.push(s))}return c};return{rows:u(e),flatRows:r,rowsById:i}}function lt(e,o,t){var n;const r=[],i={},l=(n=t.options.maxLeafRowFilterDepth)!=null?n:100,u=function(a,g){g===void 0&&(g=0);const c=[];for(let d=0;d<a.length;d++){let s=a[d];if(o(s)){var S;if((S=s.subRows)!=null&&S.length&&g<l){const p=Z(t,s.id,s.original,s.index,s.depth,void 0,s.parentId);p.subRows=u(s.subRows,g+1),s=p}c.push(s),r.push(s),i[s.id]=s}}return c};return{rows:u(e),flatRows:r,rowsById:i}}function ft(){return e=>m(()=>[e.getPreFilteredRowModel(),e.getState().columnFilters,e.getState().globalFilter],(o,t,n)=>{if(!o.rows.length||!(t!=null&&t.length)&&!n){for(let d=0;d<o.flatRows.length;d++)o.flatRows[d].columnFilters={},o.flatRows[d].columnFiltersMeta={};return o}const r=[],i=[];(t??[]).forEach(d=>{var s;const f=e.getColumn(d.id);if(!f)return;const p=f.getFilterFn();p&&r.push({id:d.id,filterFn:p,resolvedValue:(s=p.resolveFilterValue==null?void 0:p.resolveFilterValue(d.value))!=null?s:d.value})});const l=(t??[]).map(d=>d.id),u=e.getGlobalFilterFn(),a=e.getAllLeafColumns().filter(d=>d.getCanGlobalFilter());n&&u&&a.length&&(l.push("__global__"),a.forEach(d=>{var s;i.push({id:d.id,filterFn:u,resolvedValue:(s=u.resolveFilterValue==null?void 0:u.resolveFilterValue(n))!=null?s:n})}));let g,c;for(let d=0;d<o.flatRows.length;d++){const s=o.flatRows[d];if(s.columnFilters={},r.length)for(let f=0;f<r.length;f++){g=r[f];const p=g.id;s.columnFilters[p]=g.filterFn(s,p,g.resolvedValue,R=>{s.columnFiltersMeta[p]=R})}if(i.length){for(let f=0;f<i.length;f++){c=i[f];const p=c.id;if(c.filterFn(s,p,c.resolvedValue,R=>{s.columnFiltersMeta[p]=R})){s.columnFilters.__global__=!0;break}}s.columnFilters.__global__!==!0&&(s.columnFilters.__global__=!1)}}const S=d=>{for(let s=0;s<l.length;s++)if(d.columnFilters[l[s]]===!1)return!1;return!0};return rt(o.rows,S,e)},C(e.options,"debugTable","getFilteredRowModel",()=>e._autoResetPageIndex()))}function pt(e){return o=>m(()=>[o.getState().pagination,o.getPrePaginationRowModel(),o.options.paginateExpandedRows?void 0:o.getState().expanded],(t,n)=>{if(!n.rows.length)return n;const{pageSize:r,pageIndex:i}=t;let{rows:l,flatRows:u,rowsById:a}=n;const g=r*i,c=g+r;l=l.slice(g,c);let S;o.options.paginateExpandedRows?S={rows:l,flatRows:u,rowsById:a}:S=ot({rows:l,flatRows:u,rowsById:a}),S.flatRows=[];const d=s=>{S.flatRows.push(s),s.subRows.length&&s.subRows.forEach(d)};return S.rows.forEach(d),S},C(o.options,"debugTable"))}function St(){return e=>m(()=>[e.getState().sorting,e.getPreSortedRowModel()],(o,t)=>{if(!t.rows.length||!(o!=null&&o.length))return t;const n=e.getState().sorting,r=[],i=n.filter(a=>{var g;return(g=e.getColumn(a.id))==null?void 0:g.getCanSort()}),l={};i.forEach(a=>{const g=e.getColumn(a.id);g&&(l[a.id]={sortUndefined:g.columnDef.sortUndefined,invertSorting:g.columnDef.invertSorting,sortingFn:g.getSortingFn()})});const u=a=>{const g=a.map(c=>({...c}));return g.sort((c,S)=>{for(let s=0;s<i.length;s+=1){var d;const f=i[s],p=l[f.id],R=p.sortUndefined,w=(d=f==null?void 0:f.desc)!=null?d:!1;let v=0;if(R){const h=c.getValue(f.id),V=S.getValue(f.id),F=h===void 0,E=V===void 0;if(F||E){if(R==="first")return F?-1:1;if(R==="last")return F?1:-1;v=F&&E?0:F?R:-R}}if(v===0&&(v=p.sortingFn(c,S,f.id)),v!==0)return w&&(v*=-1),p.invertSorting&&(v*=-1),v}return c.index-S.index}),g.forEach(c=>{var S;r.push(c),(S=c.subRows)!=null&&S.length&&(c.subRows=u(c.subRows))}),g};return{rows:u(t.rows),flatRows:r,rowsById:t.rowsById}},C(e.options,"debugTable","getSortedRowModel",()=>e._autoResetPageIndex()))}/**
   * react-table
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function mt(e,o){return e?st(e)?X.createElement(e,o):e:null}function st(e){return ut(e)||typeof e=="function"||at(e)}function ut(e){return typeof e=="function"&&(()=>{const o=Object.getPrototypeOf(e);return o.prototype&&o.prototype.isReactComponent})()}function at(e){return typeof e=="object"&&typeof e.$$typeof=="symbol"&&["react.memo","react.forward_ref"].includes(e.$$typeof.description)}function Ct(e){const o={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[t]=X.useState(()=>({current:nt(o)})),[n,r]=X.useState(()=>t.current.initialState);return t.current.setOptions(i=>({...i,...e,state:{...n,...e.state},onStateChange:l=>{r(l),e.onStateChange==null||e.onStateChange(l)}})),t.current}function Rt({className:e,...o}){return y.jsx("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:y.jsx("table",{"data-slot":"table",className:D("w-full caption-bottom text-sm",e),...o})})}function wt({className:e,...o}){return y.jsx("thead",{"data-slot":"table-header",className:D("[&_tr]:border-b",e),...o})}function vt({className:e,...o}){return y.jsx("tbody",{"data-slot":"table-body",className:D("[&_tr:last-child]:border-0",e),...o})}function ht({className:e,...o}){return y.jsx("tr",{"data-slot":"table-row",className:D("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...o})}function _t({className:e,...o}){return y.jsx("th",{"data-slot":"table-head",className:D("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...o})}function Ft({className:e,...o}){return y.jsx("td",{"data-slot":"table-cell",className:D("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...o})}export{Rt as T,wt as a,ht as b,dt as c,_t as d,vt as e,mt as f,Ft as g,pt as h,ct as i,ft as j,St as k,Ct as u};
