namespace RealEstate.Application.DTO.Analytics
{
    public class PropertyAnalyticsFilterDto
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public List<string> PropertyStatuses { get; set; } = new List<string>();
        public List<string> SpendingTypes { get; set; } = new List<string>();
        public decimal? MinSpent { get; set; }
        public decimal? MaxSpent { get; set; }
        public int? MinViews { get; set; }
        public int? MaxViews { get; set; }
        public string SortBy { get; set; } = "CreatedAt";
        public bool SortDescending { get; set; } = true;
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 50;
    }
}
