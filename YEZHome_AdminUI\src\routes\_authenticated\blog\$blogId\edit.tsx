import { createFileRoute } from "@tanstack/react-router";
import { EditBlogPostPage } from "@/features/blog-management/components/EditBlogPostPage";
import { Loading } from "@/components/ui/loading";

export const Route = createFileRoute("/_authenticated/blog/$blogId/edit")({
  component: EditBlogPostPage,
  beforeLoad: () => {
    return {
      getTitle: () => 'Chỉnh sửa tin tức'
    };
  },
  pendingComponent: () => <Loading />,
  errorComponent: () => <div className="p-10">Đ<PERSON> xảy ra lỗi khi tải dữ liệu.</div>,
});