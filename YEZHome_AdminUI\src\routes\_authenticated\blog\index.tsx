import { createFileRoute } from "@tanstack/react-router";
import { BlogPostsPage } from "@/features/blog-management/components/BlogPostsPage";
import { Loading } from "@/components/ui/loading";

export const Route = createFileRoute("/_authenticated/blog/")({
  component: BlogPostsPage,
  beforeLoad: () => {
    return {
      getTitle: () => 'Quản lý tin tức'
    };
  },
  pendingComponent: () => <Loading />,
  errorComponent: () => <div className="p-10"><PERSON><PERSON> xảy ra lỗi khi tải dữ liệu.</div>,
}); 