import{r as p,n as A,o as k,j as s,q as $,t as z,v as G,w as O,x as _,y as P,A as j,z as w,C as H,D as L,E as K,G as q,H as J,I as U,J as V,K as W,M as Z,N as B}from"./index-DFXNGaQ4.js";var C="Popover",[E,de]=z(C,[A]),g=A(),[Q,d]=E(C),b=e=>{const{__scopePopover:n,children:o,open:a,defaultOpen:t,onOpenChange:r,modal:c=!1}=e,i=g(n),l=p.useRef(null),[u,h]=p.useState(!1),[x,f]=k({prop:a,defaultProp:t??!1,onChange:r,caller:C});return s.jsx($,{...i,children:s.jsx(Q,{scope:n,contentId:G(),triggerRef:l,open:x,onOpenChange:f,onOpenToggle:p.useCallback(()=>f(m=>!m),[f]),hasCustomAnchor:u,onCustomAnchorAdd:p.useCallback(()=>h(!0),[]),onCustomAnchorRemove:p.useCallback(()=>h(!1),[]),modal:c,children:o})})};b.displayName=C;var N="PopoverAnchor",X=p.forwardRef((e,n)=>{const{__scopePopover:o,...a}=e,t=d(N,o),r=g(o),{onCustomAnchorAdd:c,onCustomAnchorRemove:i}=t;return p.useEffect(()=>(c(),()=>i()),[c,i]),s.jsx(j,{...r,...a,ref:n})});X.displayName=N;var F="PopoverTrigger",S=p.forwardRef((e,n)=>{const{__scopePopover:o,...a}=e,t=d(F,o),r=g(o),c=O(n,t.triggerRef),i=s.jsx(_.button,{type:"button","aria-haspopup":"dialog","aria-expanded":t.open,"aria-controls":t.contentId,"data-state":T(t.open),...a,ref:c,onClick:P(e.onClick,t.onOpenToggle)});return t.hasCustomAnchor?i:s.jsx(j,{asChild:!0,...r,children:i})});S.displayName=F;var R="PopoverPortal",[Y,ee]=E(R,{forceMount:void 0}),y=e=>{const{__scopePopover:n,forceMount:o,children:a,container:t}=e,r=d(R,n);return s.jsx(Y,{scope:n,forceMount:o,children:s.jsx(w,{present:o||r.open,children:s.jsx(H,{asChild:!0,container:t,children:a})})})};y.displayName=R;var v="PopoverContent",D=p.forwardRef((e,n)=>{const o=ee(v,e.__scopePopover),{forceMount:a=o.forceMount,...t}=e,r=d(v,e.__scopePopover);return s.jsx(w,{present:a||r.open,children:r.modal?s.jsx(te,{...t,ref:n}):s.jsx(re,{...t,ref:n})})});D.displayName=v;var oe=q("PopoverContent.RemoveScroll"),te=p.forwardRef((e,n)=>{const o=d(v,e.__scopePopover),a=p.useRef(null),t=O(n,a),r=p.useRef(!1);return p.useEffect(()=>{const c=a.current;if(c)return L(c)},[]),s.jsx(K,{as:oe,allowPinchZoom:!0,children:s.jsx(M,{...e,ref:t,trapFocus:o.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:P(e.onCloseAutoFocus,c=>{var i;c.preventDefault(),r.current||(i=o.triggerRef.current)==null||i.focus()}),onPointerDownOutside:P(e.onPointerDownOutside,c=>{const i=c.detail.originalEvent,l=i.button===0&&i.ctrlKey===!0,u=i.button===2||l;r.current=u},{checkForDefaultPrevented:!1}),onFocusOutside:P(e.onFocusOutside,c=>c.preventDefault(),{checkForDefaultPrevented:!1})})})}),re=p.forwardRef((e,n)=>{const o=d(v,e.__scopePopover),a=p.useRef(!1),t=p.useRef(!1);return s.jsx(M,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:r=>{var c,i;(c=e.onCloseAutoFocus)==null||c.call(e,r),r.defaultPrevented||(a.current||(i=o.triggerRef.current)==null||i.focus(),r.preventDefault()),a.current=!1,t.current=!1},onInteractOutside:r=>{var l,u;(l=e.onInteractOutside)==null||l.call(e,r),r.defaultPrevented||(a.current=!0,r.detail.originalEvent.type==="pointerdown"&&(t.current=!0));const c=r.target;((u=o.triggerRef.current)==null?void 0:u.contains(c))&&r.preventDefault(),r.detail.originalEvent.type==="focusin"&&t.current&&r.preventDefault()}})}),M=p.forwardRef((e,n)=>{const{__scopePopover:o,trapFocus:a,onOpenAutoFocus:t,onCloseAutoFocus:r,disableOutsidePointerEvents:c,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:u,onInteractOutside:h,...x}=e,f=d(v,o),m=g(o);return J(),s.jsx(U,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:t,onUnmountAutoFocus:r,children:s.jsx(V,{asChild:!0,disableOutsidePointerEvents:c,onInteractOutside:h,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:u,onDismiss:()=>f.onOpenChange(!1),children:s.jsx(W,{"data-state":T(f.open),role:"dialog",id:f.contentId,...m,...x,ref:n,style:{...x.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),I="PopoverClose",ne=p.forwardRef((e,n)=>{const{__scopePopover:o,...a}=e,t=d(I,o);return s.jsx(_.button,{type:"button",...a,ref:n,onClick:P(e.onClick,()=>t.onOpenChange(!1))})});ne.displayName=I;var ae="PopoverArrow",se=p.forwardRef((e,n)=>{const{__scopePopover:o,...a}=e,t=g(o);return s.jsx(Z,{...t,...a,ref:n})});se.displayName=ae;function T(e){return e?"open":"closed"}var ce=b,ie=S,pe=y,le=D;function fe({...e}){return s.jsx(ce,{"data-slot":"popover",...e})}function ve({...e}){return s.jsx(ie,{"data-slot":"popover-trigger",...e})}function Pe({className:e,align:n="center",sideOffset:o=4,...a}){return s.jsx(pe,{children:s.jsx(le,{"data-slot":"popover-content",align:n,sideOffset:o,className:B("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",e),...a})})}export{fe as P,ve as a,Pe as b};
