import { useQuery } from '@tanstack/react-query'
import userService from '@/services/user-service'

interface EmployeeSearchParams {
  PageNumber?: number
  PageSize?: number
  Email?: string
  Name?: string
  Phone?: string
  SortColumn?: string
  SortDescending?: boolean
}

export const useEmployeeData = (searchParams: EmployeeSearchParams) => {
  return useQuery({
    queryKey: ['employees', searchParams],
    queryFn: () => userService.getEmployees(searchParams),
  })
}

export const useEmployeeDetails = (employeeId: string | undefined, enabled: boolean = true) => {
  return useQuery({
    queryKey: ['employee', employeeId],
    queryFn: () => employeeId ? userService.getUserById(employeeId) : null,
    enabled: !!employeeId && enabled,
  })
}

export const useRoles = (enabled: boolean = true) => {
  return useQuery({
    queryKey: ['roles'],
    queryFn: () => userService.getRoles(),
    enabled,
  })
} 