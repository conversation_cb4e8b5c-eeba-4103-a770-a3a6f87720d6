import { Building2, LayoutDashboard, Settings, Users, FileText, Bell } from "lucide-react"
import { Link } from "@tanstack/react-router"

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import { NavUser } from "./nav-user";
import { useAuthStore } from "@/store/auth-store";

const activeProps = {
  className: "bg-primary text-primary-foreground",
};

// Menu items.
const items = [
  {
    title: "Dashboard",
    url: "/dashboard",
    icon: LayoutDashboard,
  },
  {
    title: "Bất động sản",
    url: "/property",
    icon: Building2,
  },
  {
    title: "Người dùng",
    url: "/customer",
    icon: Users,
  },
  {
    title: "Nhân viên",
    url: "/employee",
    icon: Users,
  },
  {
    title: "Tin tức",
    url: "/blog",
    icon: FileText,
  },
  {
    title: "Thông báo",
    url: "/notification",
    icon: Bell,
  },
  {
    title: "Cài đặt",
    url: "/setting",
    icon: Settings,
  },
]

export function AppSidebar(props: React.ComponentProps<typeof Sidebar>) {
  const { user } = useAuthStore();
  return (
    <Sidebar variant="inset" collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild className="hover:bg-transparent">
              <Link to="/dashboard" className="hover:bg-transparent">
                <img src="/logo.png" alt="logo" className="w-auto h-auto max-h-13" />
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel></SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {items.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <Link to={item.url} activeProps={activeProps}>
                      <item.icon />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={user!} />
      </SidebarFooter>
    </Sidebar>
  )
}