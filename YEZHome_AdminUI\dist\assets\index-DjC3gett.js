import{c as E,r as n,a as R,l as K,m as z,Y as P,j as e,B as a,P as B,b as I,L as V}from"./index-DFXNGaQ4.js";import{b as M}from"./blog-service-Dpp8ABmX.js";import{u as F,j as H,k as L,h as q,i as Q,T as X,a as _,b as w,d as U,f as k,e as $,g as A}from"./table-BxBNKVGC.js";import{I as G}from"./input-D3ozjVi3.js";import{A as Y,h as Z,a as J,b as O,c as W,d as ee,e as se,f as te,g as ae}from"./alert-dialog-CINOfzqV.js";import{S as ie}from"./search-Crlu67m_.js";import{E as ne}from"./eye-DjtIFTil.js";import{P as le}from"./pencil-DReR870U.js";import{T as oe}from"./trash-2-BZu0p0bP.js";import{C as re,a as ce,b as de,d as he}from"./card-Dikj3j-6.js";import{P as ge}from"./plus-DZShkg7N.js";/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const me=[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]],T=E("ChevronsUpDown",me);function xe({data:x,pageCount:r,onPaginationChange:c,currentPage:l,pageSize:d,onSearchChange:u,onSortChange:h}){var D;const[g,j]=n.useState([]),[v,C]=n.useState([]),[i,f]=n.useState(""),b=R(),S=K(),N=z({mutationFn:s=>M.deleteBlogPost(s),onSuccess:()=>{S.invalidateQueries({queryKey:["blogPosts"]}),P({title:"Thành công",description:"Bài viết đã được xóa thành công.",variant:"success"})},onError:s=>{P({title:"Lỗi",description:"Không thể xóa bài viết. Vui lòng thử lại sau.",variant:"destructive"}),console.error("Error deleting blog post:",s)}});n.useEffect(()=>{if(g.length>0&&h){const{id:s,desc:t}=g[0];h(s,t?"desc":"asc")}},[g,h]);const p=()=>{u&&u(i)},o=s=>{s.key==="Enter"&&p()},m=[{accessorKey:"title",header:({column:s})=>e.jsxs(a,{variant:"ghost",onClick:()=>s.toggleSorting(s.getIsSorted()==="asc"),className:"pl-0",children:["Tiêu đề",e.jsx(T,{className:"ml-2 h-4"})]}),cell:({row:s})=>e.jsx("div",{className:"max-w-32 truncate font-medium sm:max-w-72 md:max-w-[31rem]",children:s.getValue("title")})},{accessorKey:"status",header:({column:s})=>e.jsxs(a,{variant:"ghost",onClick:()=>s.toggleSorting(s.getIsSorted()==="asc"),children:["Trạng thái",e.jsx(T,{className:"ml-2 h-4 w-4"})]}),cell:({row:s})=>e.jsx("div",{className:"capitalize",children:s.getValue("status")})},{accessorKey:"publishedAt",header:({column:s})=>e.jsxs(a,{variant:"ghost",onClick:()=>s.toggleSorting(s.getIsSorted()==="asc"),children:["Ngày xuất bản",e.jsx(T,{className:"ml-2 h-4 w-4"})]}),cell:({row:s})=>{const t=s.getValue("publishedAt");return t?e.jsx("div",{children:new Date(t).toLocaleDateString("vi-VN")}):e.jsx("div",{children:"Chưa xuất bản"})}},{id:"actions",cell:({row:s})=>{const t=s.original;return e.jsxs("div",{className:"flex gap-2",children:[e.jsx(a,{variant:"ghost",size:"icon",onClick:()=>b({to:`/blog/${t.id}`}),title:"Xem chi tiết",children:e.jsx(ne,{className:"h-4 w-4"})}),e.jsx(a,{variant:"ghost",size:"icon",onClick:()=>b({to:`/blog/${t.id}/edit`}),title:"Chỉnh sửa",children:e.jsx(le,{className:"h-4 w-4"})}),e.jsxs(Y,{children:[e.jsx(Z,{asChild:!0,children:e.jsx(a,{variant:"ghost",size:"icon",title:"Xóa",children:e.jsx(oe,{className:"h-4 w-4 text-red-500"})})}),e.jsxs(J,{children:[e.jsxs(O,{children:[e.jsx(W,{children:"Xác nhận xóa"}),e.jsxs(ee,{children:['Bạn có chắc chắn muốn xóa bài viết "',t.title,'"? Hành động này không thể hoàn tác.']})]}),e.jsxs(se,{children:[e.jsx(te,{children:"Hủy"}),e.jsx(ae,{onClick:()=>N.mutate(t.id),className:"bg-red-500 hover:bg-red-600",children:"Xóa"})]})]})]})]})}}],y=F({data:x,columns:m,getCoreRowModel:Q(),getPaginationRowModel:q(),onSortingChange:j,getSortedRowModel:L(),onColumnFiltersChange:C,getFilteredRowModel:H(),state:{sorting:g,columnFilters:v,pagination:{pageIndex:l-1,pageSize:d}},manualPagination:!0,pageCount:r});return e.jsxs("div",{children:[e.jsx("div",{className:"flex items-center justify-between py-4",children:e.jsxs("div",{className:"flex items-center gap-2 max-w-sm",children:[e.jsx(G,{placeholder:"Tìm kiếm theo tiêu đề...",value:i,onChange:s=>f(s.target.value),onKeyPress:o,className:"flex-1"}),e.jsx(a,{onClick:p,size:"sm",children:e.jsx(ie,{className:"h-4 w-4"})})]})}),e.jsx("div",{className:"rounded-md border",children:e.jsxs(X,{children:[e.jsx(_,{className:"bg-muted[&_tr]:border-b bg-muted sticky top-0 z-10",children:y.getHeaderGroups().map(s=>e.jsx(w,{children:s.headers.map(t=>e.jsx(U,{children:t.isPlaceholder?null:k(t.column.columnDef.header,t.getContext())},t.id))},s.id))}),e.jsx($,{children:(D=y.getRowModel().rows)!=null&&D.length?y.getRowModel().rows.map(s=>e.jsx(w,{"data-state":s.getIsSelected()&&"selected",children:s.getVisibleCells().map(t=>e.jsx(A,{children:k(t.column.columnDef.cell,t.getContext())},t.id))},s.id)):e.jsx(w,{children:e.jsx(A,{colSpan:m.length,className:"h-24 text-center",children:"Không có dữ liệu"})})})]})}),e.jsxs("div",{className:"flex items-center justify-end space-x-2 py-4",children:[e.jsxs("div",{className:"text-sm text-muted-foreground",children:["Trang ",l," / ",r]}),e.jsx(a,{variant:"outline",size:"sm",onClick:()=>c(l-1,d),disabled:l===1,children:"Trước"}),e.jsx(a,{variant:"outline",size:"sm",onClick:()=>c(l+1,d),disabled:l===r,children:"Sau"})]})]})}function ue(){const[x,r]=n.useState(1),[c,l]=n.useState(B),[d,u]=n.useState(""),[h,g]=n.useState(void 0),[j,v]=n.useState(void 0),C=R(),{data:i,isLoading:f,error:b}=I({queryKey:["blogPosts",x,c,d,h,j],queryFn:()=>M.getBlogPosts({PageNumber:x,PageSize:c,Title:d||void 0,SortColumn:h,SortDescending:j})}),S=(o,m)=>{r(o),l(m)},N=o=>{u(o),r(1)},p=(o,m)=>{g(o),v(m==="desc")};return f?e.jsx(V,{}):b?e.jsxs("div",{className:"text-center py-10",children:[e.jsx("h2",{className:"text-2xl font-bold text-red-600",children:"Đã xảy ra lỗi"}),e.jsx("p",{className:"text-gray-600",children:"Không thể tải danh sách bài viết. Vui lòng thử lại sau."})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Quản lý bài viết"}),e.jsxs(a,{onClick:()=>C({to:"/blog/new"}),children:[e.jsx(ge,{className:"mr-2 h-4 w-4"})," Tạo bài viết mới"]})]}),e.jsxs(re,{children:[e.jsx(ce,{children:e.jsx(de,{className:"text-xl",children:"Danh sách bài viết"})}),e.jsx(he,{children:e.jsx(xe,{data:(i==null?void 0:i.items)||[],pageCount:(i==null?void 0:i.pageCount)||0,onPaginationChange:S,currentPage:x,pageSize:c,onSearchChange:N,onSortChange:p})})]})]})}const De=ue;export{De as component};
