import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from '@/components/ui/use-toast'
import userService from '@/services/user-service'
import type { UpdateUserRoleDto } from '@/lib/types/role'

export const useCreateEmployee = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: userService.createEmployee,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] })
      toast({
        title: "Thành công",
        description: "Đã thêm nhân viên mới",
        variant: "success",
      })
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: "Không thể thêm nhân viên. Vui lòng thử lại.",
        variant: "destructive",
      })
      console.error('Error creating employee:', error)
    }
  })
}

export const useUpdateEmployeeStatus = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, status }: { id: string, status: boolean }) => 
      userService.updateUserStatus(id, { isActive: status }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] })
      queryClient.invalidateQueries({ queryKey: ['employee'] })
      toast({
        title: "Thành công",
        description: "Đã cập nhật trạng thái nhân viên",
        variant: "success",
      })
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: "Không thể cập nhật trạng thái nhân viên. Vui lòng thử lại.",
        variant: "destructive",
      })
      console.error('Error updating employee status:', error)
    }
  })
}

export const useUpdateEmployeeRoles = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (data: UpdateUserRoleDto) => userService.updateUserRoles(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] })
      queryClient.invalidateQueries({ queryKey: ['employee'] })
      toast({
        title: "Thành công",
        description: "Đã cập nhật vai trò nhân viên",
        variant: "success",
      })
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: "Không thể cập nhật vai trò nhân viên. Vui lòng thử lại.",
        variant: "destructive",
      })
      console.error('Error updating employee roles:', error)
    }
  })
}

export const useDeleteEmployee = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (id: string) => userService.deleteUser(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] })
      toast({
        title: "Thành công",
        description: "Đã xóa nhân viên",
        variant: "success",
      })
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: "Không thể xóa nhân viên. Vui lòng thử lại.",
        variant: "destructive",
      })
      console.error('Error deleting employee:', error)
    }
  })
} 