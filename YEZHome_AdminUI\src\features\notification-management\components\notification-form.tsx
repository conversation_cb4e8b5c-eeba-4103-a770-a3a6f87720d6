import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { useToast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';
import notificationService from '@/services/notification-service';
import type { NotificationCreateRequest, NotificationType } from '@/lib/types/notification';
import { notificationTypeMap } from '@/lib/types/notification';
import { NOTIFICATION_TYPE } from '@/lib/enum';

// Form validation schema
const notificationFormSchema = z.object({
  type: z.enum([NOTIFICATION_TYPE.SYSTEM, NOTIFICATION_TYPE.PROMOTION, NOTIFICATION_TYPE.NEWS], {
    message: 'Loại thông báo là bắt buộc',
  }),
  title: z.string().min(1, 'Tiêu đề là bắt buộc').max(200, 'Tiêu đề không được vượt quá 200 ký tự'),
  message: z.string().min(1, 'Nội dung là bắt buộc').max(1000, 'Nội dung không được vượt quá 1000 ký tự'),
  relatedEntityId: z.string().optional(),
  relatedPropertyId: z.string().optional(),
  actionUrl: z.string().url('URL không hợp lệ').optional().or(z.literal('')),
});

type NotificationFormData = z.infer<typeof notificationFormSchema>;

interface NotificationFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export const NotificationForm = ({ onSuccess, onCancel }: NotificationFormProps) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<NotificationFormData>({
    resolver: zodResolver(notificationFormSchema),
    defaultValues: {
      type: undefined,
      title: '',
      message: '',
      relatedEntityId: '',
      relatedPropertyId: '',
      actionUrl: '',
    },
  });

  const createNotificationMutation = useMutation({
    mutationFn: (data: NotificationCreateRequest) => notificationService.createNotification(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      toast({
        title: 'Thành công',
        description: 'Thông báo đã được tạo thành công',
        variant: 'success',
      });
      form.reset();
      onSuccess?.();
    },
    onError: (error: any) => {
      toast({
        title: 'Lỗi',
        description: error.response?.data?.message || 'Có lỗi xảy ra khi tạo thông báo',
        variant: 'destructive',
      });
    },
    onSettled: () => {
      setIsSubmitting(false);
    },
  });

  const onSubmit = async (data: NotificationFormData) => {
    setIsSubmitting(true);
    
    const submitData: NotificationCreateRequest = {
      type: data.type,
      title: data.title,
      message: data.message,
      relatedEntityId: data.relatedEntityId || undefined,
      relatedPropertyId: data.relatedPropertyId || undefined,
      actionUrl: data.actionUrl || undefined,
    };

    createNotificationMutation.mutate(submitData);
  };

  // Type options for select
  const typeOptions = Object.entries(notificationTypeMap).map(([value, label]) => ({
    value: value as NotificationType,
    label,
  }));

  return (
    <Card>
      <CardHeader>
        <CardTitle>Tạo thông báo mới</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Type */}
              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Loại thông báo *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Chọn loại thông báo" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {typeOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Title */}
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tiêu đề *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Nhập tiêu đề thông báo"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Message */}
            <FormField
              control={form.control}
              name="message"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nội dung *</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Nhập nội dung thông báo"
                      rows={4}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Related Entity ID */}
              <FormField
                control={form.control}
                name="relatedEntityId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>ID thực thể liên quan</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Nhập ID thực thể liên quan (tùy chọn)"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Related Property ID */}
              <FormField
                control={form.control}
                name="relatedPropertyId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>ID bất động sản liên quan</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Nhập ID bất động sản liên quan (tùy chọn)"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Action URL */}
            <FormField
              control={form.control}
              name="actionUrl"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>URL hành động</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Nhập URL hành động (tùy chọn)"
                      type="url"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Actions */}
            <div className="flex gap-4 pt-4 align-center justify-end">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isSubmitting}
                >
                  Hủy
                </Button>
              )}
              <Button
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Tạo thông báo
              </Button>
              
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}; 