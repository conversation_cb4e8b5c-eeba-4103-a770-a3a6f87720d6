import{c as o,r as L,j as e,t as ee,x as K,N as se,al as y,a as te,k as ae,l as ne,a7 as re,b as D,m as ie,W as b,B as E,F as w,L as k,Z as le}from"./index-DFXNGaQ4.js";import{C as m,a as p,b as g,d as u}from"./card-Dikj3j-6.js";import{B as C}from"./badge-BmdoMzu9.js";import{A as ce,a as de,b as oe,c as xe,d as he,e as me,f as pe,g as ge}from"./alert-dialog-CINOfzqV.js";import{A as ue}from"./arrow-left-CbfRM3Sk.js";import{U as S}from"./user-Bt4NuokJ.js";import{C as H}from"./calendar-CjeZn9Ho.js";import{C as je}from"./chart-column-CFmW2pC9.js";import{C as Ne,M as fe}from"./map-pin-J3Sp8w3O.js";import{E as ve}from"./eye-DjtIFTil.js";/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ye=[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]],A=o("Building",ye);/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const be=[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]],q=o("CreditCard",be);/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const we=[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]],ke=o("Heart",we);/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ce=[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]],z=o("Mail",Ce);/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Se=[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]],M=o("Phone",Se);/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ae=[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]],P=o("Star",Ae);/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Me=[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]],Pe=o("TrendingUp",Me);/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Le=[["path",{d:"M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1",key:"18etb6"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4",key:"xoc0q4"}]],Te=o("Wallet",Le);var T="Progress",_=100,[_e,Ze]=ee(T),[Re,$e]=_e(T),O=L.forwardRef((a,t)=>{const{__scopeProgress:x,value:c=null,max:n,getValueLabel:j=Ie,...f}=a;(n||n===0)&&!F(n)&&console.error(Ve(`${n}`,"Progress"));const r=F(n)?n:_;c!==null&&!U(c,r)&&console.error(Be(`${c}`,"Progress"));const d=U(c,r)?c:null,s=N(d)?j(d,r):void 0;return e.jsx(Re,{scope:x,value:d,max:r,children:e.jsx(K.div,{"aria-valuemax":r,"aria-valuemin":0,"aria-valuenow":N(d)?d:void 0,"aria-valuetext":s,role:"progressbar","data-state":Q(d,r),"data-value":d??void 0,"data-max":r,...f,ref:t})})});O.displayName=T;var X="ProgressIndicator",G=L.forwardRef((a,t)=>{const{__scopeProgress:x,...c}=a,n=$e(X,x);return e.jsx(K.div,{"data-state":Q(n.value,n.max),"data-value":n.value??void 0,"data-max":n.max,...c,ref:t})});G.displayName=X;function Ie(a,t){return`${Math.round(a/t*100)}%`}function Q(a,t){return a==null?"indeterminate":a===t?"complete":"loading"}function N(a){return typeof a=="number"}function F(a){return N(a)&&!isNaN(a)&&a>0}function U(a,t){return N(a)&&!isNaN(a)&&a<=t&&a>=0}function Ve(a,t){return`Invalid prop \`max\` of value \`${a}\` supplied to \`${t}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${_}\`.`}function Be(a,t){return`Invalid prop \`value\` of value \`${a}\` supplied to \`${t}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${_} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var De=O,Ee=G;function He({className:a,value:t,...x}){return e.jsx(De,{"data-slot":"progress",className:se("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",a),...x,children:e.jsx(Ee,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})})}const Je=function(){var R,$,I,V,B;const t=y.useLoaderData(),x=te();y.useSearch();const[c,n]=L.useState(!1),{toast:j}=ae(),f=ne(),r=y.useParams(),{history:d}=re(),{data:s,isLoading:v}=D({queryKey:["customerDashboard",r.customerId],queryFn:()=>b.getUserDashboard(r.customerId)}),{data:i,isLoading:W}=D({queryKey:["customerInvoiceInfo",r.customerId],queryFn:()=>b.getUserInvoiceInfo(r.customerId)}),Z=ie({mutationFn:async l=>await b.updateUserStatus(r.customerId,{isActive:l}),onSuccess:()=>{f.invalidateQueries({queryKey:["customer",r.customerId]}),j({title:"Cập nhật trạng thái thành công",description:"Trạng thái khách hàng đã được cập nhật",variant:"success"}),n(!1),x({to:`/customer/${r.customerId}`,replace:!0})},onError:l=>{console.log(l),j({title:"Lỗi cập nhật trạng thái",description:"Đã xảy ra lỗi khi cập nhật trạng thái khách hàng",variant:"destructive"})}}),J=()=>{Z.mutate(!t.isActive)},h=l=>new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(l);return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(E,{variant:"outline",size:"icon",onClick:()=>{d.go(-1)},children:e.jsx(ue,{className:"h-4 w-4"})}),e.jsx("h1",{className:"text-2xl font-bold",children:"Chi tiết khách hàng"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(C,{variant:"subtle",colorScheme:t.isActive?"green":"red",className:"px-3 py-1 text-sm",showDot:!0,dotColor:t.isActive?"green":"red",children:t.isActive?"Đang hoạt động":"Đã vô hiệu hóa"}),e.jsx(E,{variant:t.isActive?"destructive":"default",onClick:()=>n(!0),children:t.isActive?"Vô hiệu hóa":"Kích hoạt"})]})]}),e.jsxs(m,{children:[e.jsx(p,{children:e.jsx(g,{className:"text-xl",children:"Thông tin cá nhân"})}),e.jsx(u,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(S,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"font-semibold",children:"Họ tên:"}),e.jsx("span",{children:t.fullName||"Chưa cập nhật"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(z,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"font-semibold",children:"Email:"}),e.jsx("span",{children:t.email||"Chưa cập nhật"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(M,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"font-semibold",children:"Số điện thoại:"}),e.jsx("span",{children:t.phone||"Chưa cập nhật"})]}),t.phone2&&e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(M,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"font-semibold",children:"Số điện thoại 2:"}),e.jsx("span",{children:t.phone2})]}),t.phone3&&e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(M,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"font-semibold",children:"Số điện thoại 3:"}),e.jsx("span",{children:t.phone3})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(S,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"font-semibold",children:"Loại người dùng:"}),e.jsx("span",{children:t.userType||"Chưa xác định"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(w,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"font-semibold",children:"Mã số thuế cá nhân:"}),e.jsx("span",{children:t.personalTaxCode||"Chưa cập nhật"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(q,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"font-semibold",children:"Mã chuyển khoản:"}),e.jsx("span",{children:t.transferCode||"Chưa cập nhật"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(H,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"font-semibold",children:"Đăng nhập cuối:"}),e.jsx("span",{children:t.lastLogin?new Date(t.lastLogin).toLocaleString("vi-VN"):"Chưa đăng nhập"})]}),((R=s==null?void 0:s.userInfo)==null?void 0:R.createdAt)&&e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(H,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"font-semibold",children:"Ngày tham gia:"}),e.jsx("span",{children:new Date(s.userInfo.createdAt).toLocaleDateString("vi-VN")})]})]})]})})]}),e.jsxs(m,{children:[e.jsx(p,{children:e.jsx(g,{className:"text-xl",children:"Thông tin ví tiền"})}),e.jsx(u,{children:v?e.jsx(k,{}):e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Te,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"font-semibold",children:"Số dư:"}),e.jsx("span",{className:"font-bold text-lg",children:(($=s==null?void 0:s.walletInfo)==null?void 0:$.balance)!==void 0?h(s.walletInfo.balance):"0 ₫"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(q,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"font-semibold",children:"Tổng chi tiêu:"}),e.jsx("span",{className:"font-bold text-lg",children:((I=s==null?void 0:s.walletInfo)==null?void 0:I.totalSpent)!==void 0?h(s.walletInfo.totalSpent):"0 ₫"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Pe,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"font-semibold",children:"Tổng số giao dịch:"}),e.jsx("span",{children:((V=s==null?void 0:s.walletInfo)==null?void 0:V.totalTransactions)||0})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(je,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"font-semibold",children:"Chi tiêu tháng trước:"}),e.jsx("span",{children:((B=s==null?void 0:s.walletInfo)==null?void 0:B.lastMonthSpending)!==void 0?h(s.walletInfo.lastMonthSpending):"0 ₫"})]})]})]})})]}),e.jsxs(m,{children:[e.jsx(p,{children:e.jsx(g,{className:"text-xl",children:"Hạng thành viên"})}),e.jsx(u,{children:v?e.jsx(k,{}):s!=null&&s.memberRanking?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(P,{className:"h-5 w-5 text-yellow-500"}),e.jsx("span",{className:"font-semibold",children:"Hạng hiện tại:"}),e.jsx(C,{variant:"outline",className:"text-sm capitalize",children:s.memberRanking.currentRank||"Chưa xác định"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(P,{className:"h-5 w-5 text-blue-500"}),e.jsx("span",{className:"font-semibold",children:"Hạng tiếp theo:"}),e.jsx(C,{variant:"outline",className:"text-sm capitalize",children:s.memberRanking.nextRank||"Cao nhất"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{children:h(s.memberRanking.minSpent)}),e.jsx("span",{children:h(s.memberRanking.maxSpent)})]}),e.jsx(He,{value:s.memberRanking.progressPercentage,className:"h-2"}),e.jsxs("div",{className:"text-sm text-center",children:["Cần chi tiêu thêm ",h(s.memberRanking.spendingToNextRank)," để lên hạng ",s.memberRanking.nextRank]})]})]}):e.jsx("div",{className:"text-center py-4 text-muted-foreground",children:"Không có thông tin hạng thành viên"})})]}),e.jsxs(m,{children:[e.jsx(p,{children:e.jsx(g,{className:"text-xl",children:"Thống kê bất động sản"})}),e.jsx(u,{children:v?e.jsx("div",{className:"flex justify-center py-8",children:e.jsx(le,{})}):s!=null&&s.propertyStats?e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"bg-muted rounded-lg p-4 text-center",children:[e.jsx(A,{className:"h-6 w-6 mx-auto mb-2 text-primary"}),e.jsx("div",{className:"text-2xl font-bold",children:s.propertyStats.totalProperties}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Tổng số BĐS"})]}),e.jsxs("div",{className:"bg-muted rounded-lg p-4 text-center",children:[e.jsx(A,{className:"h-6 w-6 mx-auto mb-2 text-green-500"}),e.jsx("div",{className:"text-2xl font-bold",children:s.propertyStats.activeProperties}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"BĐS đang hoạt động"})]}),e.jsxs("div",{className:"bg-muted rounded-lg p-4 text-center",children:[e.jsx(Ne,{className:"h-6 w-6 mx-auto mb-2 text-yellow-500"}),e.jsx("div",{className:"text-2xl font-bold",children:s.propertyStats.expiredProperties}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"BĐS hết hạn"})]}),e.jsxs("div",{className:"bg-muted rounded-lg p-4 text-center",children:[e.jsx(w,{className:"h-6 w-6 mx-auto mb-2 text-blue-500"}),e.jsx("div",{className:"text-2xl font-bold",children:s.propertyStats.draftProperties}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"BĐS nháp"})]}),e.jsxs("div",{className:"bg-muted rounded-lg p-4 text-center",children:[e.jsx(ke,{className:"h-6 w-6 mx-auto mb-2 text-red-500"}),e.jsx("div",{className:"text-2xl font-bold",children:s.propertyStats.favoriteProperties}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"BĐS yêu thích"})]}),e.jsxs("div",{className:"bg-muted rounded-lg p-4 text-center",children:[e.jsx(ve,{className:"h-6 w-6 mx-auto mb-2 text-purple-500"}),e.jsx("div",{className:"text-2xl font-bold",children:s.propertyStats.totalViews}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Lượt xem"})]}),e.jsxs("div",{className:"bg-muted rounded-lg p-4 text-center",children:[e.jsx(P,{className:"h-6 w-6 mx-auto mb-2 text-amber-500"}),e.jsx("div",{className:"text-2xl font-bold",children:s.propertyStats.averageRating||0}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Đánh giá trung bình"})]})]}):e.jsx("div",{className:"text-center py-4 text-muted-foreground",children:"Không có thống kê bất động sản"})})]}),(s==null?void 0:s.recentTransactions)&&s.recentTransactions.length>0&&e.jsxs(m,{children:[e.jsx(p,{children:e.jsx(g,{className:"text-xl",children:"Giao dịch gần đây"})}),e.jsx(u,{children:e.jsx("div",{className:"rounded-md border",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-muted",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider",children:"Ngày"}),e.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider",children:"Loại"}),e.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider",children:"Mô tả"}),e.jsx("th",{className:"px-4 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider",children:"Số tiền"})]})}),e.jsx("tbody",{className:"bg-card divide-y divide-gray-200",children:s.recentTransactions.map((l,Y)=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm",children:l.date?new Date(l.date).toLocaleDateString("vi-VN"):"-"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm",children:l.type||"-"}),e.jsx("td",{className:"px-4 py-3 text-sm",children:l.description||"-"}),e.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-right",children:l.amount!==void 0?h(l.amount):"-"})]},Y))})]})})})]}),e.jsxs(m,{children:[e.jsx(p,{children:e.jsx(g,{className:"text-xl",children:"Thông tin xuất hóa đơn"})}),e.jsx(u,{children:W?e.jsx(k,{}):e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(S,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"font-semibold",children:"Họ tên:"}),e.jsx("span",{children:(i==null?void 0:i.buyerName)||"Chưa cập nhật"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(z,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"font-semibold",children:"Email:"}),e.jsx("span",{children:(i==null?void 0:i.email)||"Chưa cập nhật"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(A,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"font-semibold",children:"Tên công ty:"}),e.jsx("span",{children:(i==null?void 0:i.companyName)||"Chưa cập nhật"})]})]}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(w,{className:"h-4 w-4 text-muted-foreground"}),e.jsx("span",{className:"font-semibold",children:"Mã số thuế:"}),e.jsx("span",{children:(i==null?void 0:i.taxCode)||"Chưa cập nhật"})]})}),e.jsx("div",{className:"col-span-1 md:col-span-2 space-y-2",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx(fe,{className:"h-4 w-4 text-muted-foreground mt-1"}),e.jsx("span",{className:"font-semibold",children:"Địa chỉ:"}),e.jsx("span",{className:"flex-1",children:(i==null?void 0:i.address)||"Chưa cập nhật"})]})})]})})]}),e.jsx(ce,{open:c,onOpenChange:n,children:e.jsxs(de,{children:[e.jsxs(oe,{children:[e.jsx(xe,{children:t.isActive?"Xác nhận vô hiệu hóa tài khoản":"Xác nhận kích hoạt tài khoản"}),e.jsx(he,{children:t.isActive?"Bạn có chắc chắn muốn vô hiệu hóa tài khoản này? Người dùng sẽ không thể đăng nhập cho đến khi được kích hoạt lại.":"Bạn có chắc chắn muốn kích hoạt tài khoản này? Người dùng sẽ có thể đăng nhập và sử dụng dịch vụ."})]}),e.jsxs(me,{children:[e.jsx(pe,{children:"Hủy"}),e.jsx(ge,{onClick:J,className:t.isActive?"bg-red-600 hover:bg-red-700":"",children:t.isActive?"Vô hiệu hóa":"Kích hoạt"})]})]})})]})};export{Je as component};
