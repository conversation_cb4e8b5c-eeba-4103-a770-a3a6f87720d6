import { z } from "zod";
import { PAGE_SIZE } from "../enum";

// Define search schema for URL parameters
export const employeeSearchSchema = z.object({
  page: z.number().optional().default(1),
  pageSize: z.number().optional().default(PAGE_SIZE),
  email: z.string().optional().default(''),
  name: z.string().optional().default(''),
  phone: z.string().optional().default(''),
  sortColumn: z.string().optional(),
  sortDescending: z.boolean().optional(),
});

export const createEmployeeWithSingleRoleSchema = z.object({
  fullName: z.string().min(1, 'Họ tên không được để trống'),
  email: z.string().email('Email không hợp lệ').min(1, '<PERSON>ail không được để trống'),
  password: z.string().min(6, '<PERSON><PERSON><PERSON> khẩu phải có ít nhất 6 ký tự'),
  phone: z.string()
  .min(1, '<PERSON><PERSON> điện thoại không được để trống')
  .regex(/^(0[3|5|7|8|9])+([0-9]{8})$/, 'Số điện thoại không hợp lệ (phải có 10 số và bắt đầu bằng 03, 05, 07, 08, 09)'),
  roleId: z.string().min(1, 'Phải chọn vai trò'),
});

export type EmployeeSearchParams = z.infer<typeof employeeSearchSchema>;
export type CreateEmployeeFormData = z.infer<typeof createEmployeeWithSingleRoleSchema>;