import{c as I,k as v,l as C,r as S,O as T,m as F,j as e,B as u,S as d,ah as L,ai as k,a as E}from"./index-DFXNGaQ4.js";import{u as q,F as D,a as r,b as i,c as a,d as o,e as l}from"./form-lBragMB2.js";import{a as U}from"./zod-TzqjP9W3.js";import{I as m}from"./input-D3ozjVi3.js";import{T as M}from"./index-BmxYfgzo.js";import{C as P,a as O,b as w,d as R}from"./card-Dikj3j-6.js";import{S as V,a as Q,b as _,c as z,d as A}from"./select-DIbexXwn.js";import{a as B,n as H}from"./notification-BvCoxbDh.js";import{A as K}from"./arrow-left-CbfRM3Sk.js";import"./label-BscdIpbp.js";import"./index-BpksVOb4.js";import"./chevron-down-DuS99v9n.js";/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const W=[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]],Y=I("LoaderCircle",W),G=T({userId:d().min(1,"ID người dùng là bắt buộc"),type:L(["SYSTEM","PROMOTION","NEWS"],{message:"Loại thông báo là bắt buộc"}),title:d().min(1,"Tiêu đề là bắt buộc").max(200,"Tiêu đề không được vượt quá 200 ký tự"),message:d().min(1,"Nội dung là bắt buộc").max(1e3,"Nội dung không được vượt quá 1000 ký tự"),relatedEntityId:d().optional(),relatedPropertyId:d().optional(),actionUrl:d().url("URL không hợp lệ").optional().or(k(""))}),J=({onSuccess:c,onCancel:h})=>{const{toast:x}=v(),b=C(),[j,p]=S.useState(!1),s=q({resolver:U(G),defaultValues:{userId:"",type:void 0,title:"",message:"",relatedEntityId:"",relatedPropertyId:"",actionUrl:""}}),y=F({mutationFn:t=>B.createNotification(t),onSuccess:()=>{b.invalidateQueries({queryKey:["notifications"]}),x({title:"Thành công",description:"Thông báo đã được tạo thành công"}),s.reset(),c==null||c()},onError:t=>{var n,g;x({title:"Lỗi",description:((g=(n=t.response)==null?void 0:n.data)==null?void 0:g.message)||"Có lỗi xảy ra khi tạo thông báo",variant:"destructive"})},onSettled:()=>{p(!1)}}),N=async t=>{p(!0);const n={userId:t.userId,type:t.type,title:t.title,message:t.message,relatedEntityId:t.relatedEntityId||void 0,relatedPropertyId:t.relatedPropertyId||void 0,actionUrl:t.actionUrl||void 0};y.mutate(n)},f=Object.entries(H).map(([t,n])=>({value:t,label:n}));return e.jsxs(P,{children:[e.jsx(O,{children:e.jsx(w,{children:"Tạo thông báo mới"})}),e.jsx(R,{children:e.jsx(D,{...s,children:e.jsxs("form",{onSubmit:s.handleSubmit(N),className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsx(r,{control:s.control,name:"userId",render:({field:t})=>e.jsxs(i,{children:[e.jsx(a,{children:"ID người dùng *"}),e.jsx(o,{children:e.jsx(m,{placeholder:"Nhập ID người dùng",...t})}),e.jsx(l,{})]})}),e.jsx(r,{control:s.control,name:"type",render:({field:t})=>e.jsxs(i,{children:[e.jsx(a,{children:"Loại thông báo *"}),e.jsxs(V,{onValueChange:t.onChange,defaultValue:t.value,children:[e.jsx(o,{children:e.jsx(Q,{children:e.jsx(_,{placeholder:"Chọn loại thông báo"})})}),e.jsx(z,{children:f.map(n=>e.jsx(A,{value:n.value,children:n.label},n.value))})]}),e.jsx(l,{})]})})]}),e.jsx(r,{control:s.control,name:"title",render:({field:t})=>e.jsxs(i,{children:[e.jsx(a,{children:"Tiêu đề *"}),e.jsx(o,{children:e.jsx(m,{placeholder:"Nhập tiêu đề thông báo",...t})}),e.jsx(l,{})]})}),e.jsx(r,{control:s.control,name:"message",render:({field:t})=>e.jsxs(i,{children:[e.jsx(a,{children:"Nội dung *"}),e.jsx(o,{children:e.jsx(M,{placeholder:"Nhập nội dung thông báo",rows:4,...t})}),e.jsx(l,{})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsx(r,{control:s.control,name:"relatedEntityId",render:({field:t})=>e.jsxs(i,{children:[e.jsx(a,{children:"ID thực thể liên quan"}),e.jsx(o,{children:e.jsx(m,{placeholder:"Nhập ID thực thể liên quan (tùy chọn)",...t})}),e.jsx(l,{})]})}),e.jsx(r,{control:s.control,name:"relatedPropertyId",render:({field:t})=>e.jsxs(i,{children:[e.jsx(a,{children:"ID bất động sản liên quan"}),e.jsx(o,{children:e.jsx(m,{placeholder:"Nhập ID bất động sản liên quan (tùy chọn)",...t})}),e.jsx(l,{})]})})]}),e.jsx(r,{control:s.control,name:"actionUrl",render:({field:t})=>e.jsxs(i,{children:[e.jsx(a,{children:"URL hành động"}),e.jsx(o,{children:e.jsx(m,{placeholder:"Nhập URL hành động (tùy chọn)",type:"url",...t})}),e.jsx(l,{})]})}),e.jsxs("div",{className:"flex gap-4 pt-4",children:[e.jsxs(u,{type:"submit",disabled:j,className:"flex-1",children:[j&&e.jsx(Y,{className:"mr-2 h-4 w-4 animate-spin"}),"Tạo thông báo"]}),h&&e.jsx(u,{type:"button",variant:"outline",onClick:h,disabled:j,className:"flex-1",children:"Hủy"})]})]})})})]})},X=()=>{const c=E(),h=()=>{c({to:"/notification"})},x=()=>{c({to:"/notification"})};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(u,{variant:"outline",size:"sm",onClick:h,className:"gap-2",children:[e.jsx(K,{className:"h-4 w-4"}),"Quay lại"]}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Tạo thông báo mới"}),e.jsx("p",{className:"text-gray-600 mt-1",children:"Tạo thông báo mới để gửi đến người dùng"})]})]}),e.jsx(J,{onSuccess:x,onCancel:h})]})},de=X;export{de as component};
