import { createFileRoute } from "@tanstack/react-router";
import { EditBlogPostPage } from "@/features/blog-management/components/EditBlogPostPage";
import { Loading } from "@/components/ui/loading";

export const Route = createFileRoute("/_authenticated/blog/$blogId/$blogId/edit")({
  component: Edit,
  beforeLoad: () => {
    return {
      getTitle: () => 'Chỉnh sửa tin tức'
    };
  },
  pendingComponent: () => <Loading />,
  errorComponent: () => <div className="p-10">Đ<PERSON> xảy ra lỗi khi tải dữ liệu.</div>,
});

function Edit() {
  return (
    <div>
      <h1>Edit</h1>
    </div>
  )
}