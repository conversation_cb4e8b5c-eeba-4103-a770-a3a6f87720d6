import{j as r,N as e}from"./index-DFXNGaQ4.js";function s({className:a,...t}){return r.jsx("div",{"data-slot":"card",className:e("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...t})}function o({className:a,...t}){return r.jsx("div",{"data-slot":"card-header",className:e("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...t})}function n({className:a,...t}){return r.jsx("div",{"data-slot":"card-title",className:e("leading-none font-semibold",a),...t})}function c({className:a,...t}){return r.jsx("div",{"data-slot":"card-description",className:e("text-muted-foreground text-sm",a),...t})}function i({className:a,...t}){return r.jsx("div",{"data-slot":"card-content",className:e("px-6",a),...t})}function l({className:a,...t}){return r.jsx("div",{"data-slot":"card-footer",className:e("flex items-center px-6 [.border-t]:pt-6",a),...t})}export{s as C,o as a,n as b,c,i as d,l as e};
