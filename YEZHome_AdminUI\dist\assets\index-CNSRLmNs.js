import{c as u,at as p,a as j,a7 as N,b as f,j as s,L as b,B as t,F as v}from"./index-DFXNGaQ4.js";import{b as y}from"./blog-service-Dpp8ABmX.js";import{C as l,a as n,b as r,d as c}from"./card-Dikj3j-6.js";import{B as C}from"./badge-BmdoMzu9.js";import{A as d}from"./arrow-left-CbfRM3Sk.js";import{P as w}from"./pencil-DReR870U.js";import{U as T}from"./user-Bt4NuokJ.js";import{C as L}from"./calendar-CjeZn9Ho.js";/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const k=[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]],A=u("Tag",k);function B(){const{blogId:a}=p.useParams(),i=j(),{history:o}=N(),{data:e,isLoading:m,error:x}=f({queryKey:["blog",a],queryFn:()=>y.getBlogPost(a)});return m?s.jsx(b,{}):x||!e?s.jsxs("div",{className:"text-center py-10",children:[s.jsx("h2",{className:"text-2xl font-bold text-red-600",children:"Đã xảy ra lỗi"}),s.jsx("p",{className:"text-gray-600",children:"Không thể tải thông tin bài viết. Vui lòng thử lại sau."}),s.jsxs(t,{variant:"outline",className:"mt-4",onClick:()=>i({to:"/blog"}),children:[s.jsx(d,{className:"mr-2 h-4 w-4"})," Quay lại"]})]}):s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsx(t,{variant:"outline",size:"icon",onClick:()=>{o.go(-1)},children:s.jsx(d,{className:"h-4 w-4"})}),s.jsx("h1",{className:"text-2xl font-bold",children:"Chi tiết bài viết"})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(C,{variant:e.status==="published"?"success":"secondary",className:"px-3 py-1 text-sm capitalize",children:e.status||"Nháp"}),s.jsxs(t,{onClick:()=>{i({to:"/blog/$blogId/edit",params:{blogId:a},replace:!0})},children:[s.jsx(w,{className:"mr-2 h-4 w-4"})," Chỉnh sửa"]})]})]}),s.jsxs(l,{children:[s.jsxs(n,{children:[s.jsx(r,{className:"text-xl",children:e.title}),s.jsxs("div",{className:"text-sm text-muted-foreground",children:[s.jsxs("span",{children:["Tác giả: ",e.authorName]}),s.jsx("span",{className:"mx-2",children:"•"}),s.jsx("span",{children:e.publishedAt?`Xuất bản: ${new Date(e.publishedAt).toLocaleDateString("vi-VN",{day:"2-digit",month:"long",year:"numeric"})}`:"Chưa xuất bản"})]})]}),s.jsxs(c,{children:[e.featuredImage&&s.jsx("div",{className:"mb-6",children:s.jsx("img",{src:e.featuredImage,alt:e.title,className:"w-full max-h-80 object-cover rounded-md"})}),s.jsx("div",{className:"prose max-w-none",children:s.jsx("div",{dangerouslySetInnerHTML:{__html:e.content}})})]})]}),e.tags&&s.jsxs(l,{children:[s.jsx(n,{children:s.jsx(r,{className:"text-xl",children:"Thông tin bổ sung"})}),s.jsx(c,{children:s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(T,{className:"h-4 w-4 text-muted-foreground"}),s.jsx("span",{className:"font-semibold",children:"Tác giả:"}),s.jsx("span",{children:e.authorName||"Không có thông tin"})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(L,{className:"h-4 w-4 text-muted-foreground"}),s.jsx("span",{className:"font-semibold",children:"Ngày xuất bản:"}),s.jsx("span",{children:e.publishedAt?new Date(e.publishedAt).toLocaleDateString("vi-VN"):"Chưa xuất bản"})]})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(v,{className:"h-4 w-4 text-muted-foreground"}),s.jsx("span",{className:"font-semibold",children:"Trạng thái:"}),s.jsx("span",{className:"capitalize",children:e.status||"Nháp"})]}),s.jsxs("div",{className:"flex items-start gap-2",children:[s.jsx(A,{className:"h-4 w-4 text-muted-foreground mt-1"}),s.jsx("span",{className:"font-semibold",children:"Tags:"}),s.jsx("div",{className:"flex flex-wrap gap-2",children:e.tags.split(",").map((h,g)=>s.jsx("span",{className:"px-2 py-1 bg-muted rounded-md text-sm",children:h.trim()},g))})]})]})]})})]})]})}const H=B;export{H as component};
