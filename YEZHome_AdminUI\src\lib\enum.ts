// Property Status Enum
export enum PropertyStatus {
  Draft = 'Draft',
  PendingApproval = 'PendingApproval',
  Approved = 'Approved',
  RejectedByAdmin = 'RejectedByAdmin',
  RejectedDueToUnpaid = 'RejectedDueToUnpaid',
  WaitingPayment = 'WaitingPayment',
  Expired = 'Expired',
  Sold = 'Sold',
}

// Post Type Enum
export enum PostType {
  Sell = 'sell',
  Rent = 'rent',
}

// Property Type Enum
export enum PropertyType {
  can_ho = 'can_ho',
  nha_pho = 'nha_pho',
  nha_tro = 'nha_tro',
}

// Status display mapping
export const statusMap: Record<PropertyStatus, string> = {
  [PropertyStatus.Draft]: 'Nháp',
  [PropertyStatus.PendingApproval]: 'Chờ duyệt',
  [PropertyStatus.Approved]: 'Đã duyệt',
  [PropertyStatus.RejectedByAdmin]: 'Từ chối bởi Admin',
  [PropertyStatus.RejectedDueToUnpaid]: 'Từ chối do chưa thanh toán',
  [PropertyStatus.WaitingPayment]: 'Chờ thanh toán',
  [PropertyStatus.Expired]: 'Hết hạn',
  [PropertyStatus.Sold]: 'Đã bán',
}

export const statusColorMap: Record<PropertyStatus, string> = {
  [PropertyStatus.Draft]: 'bg-blue-50 text-blue-700 border-blue-200',
  [PropertyStatus.PendingApproval]: 'bg-gray-50 text-gray-700 border-gray-200',
  [PropertyStatus.Approved]: 'bg-green-50 text-green-700 border-green-200',
  [PropertyStatus.RejectedByAdmin]: 'bg-red-50 text-red-700 border-red-200',
  [PropertyStatus.RejectedDueToUnpaid]: 'bg-red-50 text-red-700 border-red-200',
  [PropertyStatus.WaitingPayment]: 'bg-yellow-50 text-yellow-700 border-yellow-200',
  [PropertyStatus.Expired]: 'bg-red-50 text-red-700 border-red-200',
  [PropertyStatus.Sold]: 'bg-green-50 text-green-700 border-green-200',
}

// Property type display mapping
export const propertyTypeMap: Record<PropertyType, string> = {
  [PropertyType.can_ho]: 'Căn hộ',
  [PropertyType.nha_pho]: 'Nhà phố',
  [PropertyType.nha_tro]: 'Nhà trọ',
}

// Post type display mapping
export const postTypeMap: Record<PostType, string> = {
  [PostType.Sell]: 'Bán',
  [PostType.Rent]: 'Cho thuê',
}

export const PAGE_SIZE = 50;


export const NOTIFICATION_TYPE = Object.freeze({
  SYSTEM: "System",
  TRANSACTION: "Transaction",
  CONTACT: "Contact",
  PROMOTION: "Promotion",
  NEWS: "News",
  WALLET_UPDATE: "WalletUpdate",
  CUSTOMER_MESSAGE: "CustomerMessage",
});

export const TRANSACTION_TYPE = Object.freeze({
  TOP_UP: "TOP_UP",
  PAYMENT_POST: "PAYMENT_POST",
  PAYMENT_HIGHLIGHT: "PAYMENT_HIGHLIGHT",
});

export const TRANSACTION_STATUS = Object.freeze({
  COMPLETED: "COMPLETED",
  PENDING: "PENDING",
  FAILED: "FAILED",
  CANCELLED: "CANCELLED",
});