import { useQuery } from '@tanstack/react-query'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Spinner } from '@/components/ui/spinner'
import userService from '@/services/user-service'
import type { UserDto } from '@/lib/types/user'

interface ViewEmployeeDialogProps {
  employee: UserDto | null
  isOpen: boolean
  onClose: () => void
}

export const ViewEmployeeDialog = ({ employee, isOpen, onClose }: ViewEmployeeDialogProps) => {
  const { data: employeeDetails, isLoading } = useQuery({
    queryKey: ['employee', employee?.id],
    queryFn: () => employee?.id ? userService.getUserById(employee.id) : null,
    enabled: !!employee?.id && isOpen,
  })

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Chi tiết nhân viên</DialogTitle>
        </DialogHeader>
        {isLoading ? (
          <div className="flex justify-center py-8">
            <Spinner />
          </div>
        ) : employeeDetails ? (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm font-medium">Họ tên</h4>
                <p>{employeeDetails.fullName || '-'}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium">Email</h4>
                <p>{employeeDetails.email || '-'}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium">Số điện thoại</h4>
                <p>{employeeDetails.phone || '-'}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium">Loại người dùng</h4>
                <p>{employeeDetails.userType || '-'}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium">Trạng thái</h4>
                <Badge variant="subtle" colorScheme={employeeDetails.isActive ? "green" : "red"}>
                  {employeeDetails.isActive ? 'Đang hoạt động' : 'Đã vô hiệu hóa'}
                </Badge>
              </div>
              <div>
                <h4 className="text-sm font-medium">Đăng nhập cuối</h4>
                <p>{employeeDetails.lastLogin ? new Date(employeeDetails.lastLogin).toLocaleString('vi-VN') : '-'}</p>
              </div>
            </div>
            
            {employeeDetails.roleObjects && employeeDetails.roleObjects.length > 0 && (
              <div>
                <h4 className="text-sm font-medium">Vai trò</h4>
                <div className="flex flex-wrap gap-2 mt-1">
                  {employeeDetails.roleObjects.map((role, index) => (
                    <Badge key={index} variant="outline">{role.roleName}</Badge>
                  ))}
                </div>
              </div>
            )}
            
            <DialogFooter>
              <Button onClick={onClose}>
                Đóng
              </Button>
            </DialogFooter>
          </div>
        ) : (
          <p className="text-center py-4">Không tìm thấy thông tin nhân viên</p>
        )}
      </DialogContent>
    </Dialog>
  )
} 