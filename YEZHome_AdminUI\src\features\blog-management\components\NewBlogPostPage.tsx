import { useNavigate, useRouter } from "@tanstack/react-router";
import { useMutation } from "@tanstack/react-query";
import { blogService } from "@/services/blog-service";
import { BlogPostForm } from "@/features/blog-management/components/blog-post-form";
import type { BlogFormValues } from "@/features/blog-management/components/blog-post-form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuthStore } from "@/store/auth-store";
import { toast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

export function NewBlogPostPage() {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const { history } = useRouter();

  const createMutation = useMutation({
    mutationFn: (data: BlogFormValues) => {
      // Thêm authorID từ user hiện tại
      return blogService.createBlogPost({
        ...data,
        authorID: user?.id || "",
      });
    },
    onSuccess: () => {
      toast({
        title: "Thành công",
        description: "Bài viết đã được tạo thành công.",
        variant: "success",
      });
      navigate({ to: "/blog" });
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: "Không thể tạo bài viết. Vui lòng thử lại sau.",
        variant: "destructive",
      });
      console.error("Error creating blog post:", error);
    },
  });

  const handleSubmit = (data: BlogFormValues) => {
    createMutation.mutate(data);
  };

  return (
    <div className="space-y-6">
      {/* Header with back button */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={() => {
            history.go(-1);
          }}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">Tạo bài viết mới</h1>
        </div>
      </div>

      {/* Blog post form */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Thông tin bài viết</CardTitle>
        </CardHeader>
        <CardContent>
          <BlogPostForm
            onSubmit={handleSubmit}
            isSubmitting={createMutation.isPending}
          />
        </CardContent>
      </Card>
    </div>
  );
} 