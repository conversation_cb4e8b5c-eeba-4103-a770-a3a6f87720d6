import axiosInstance from './axios-config';
import type {
  Notification,
  NotificationCreateRequest,
  NotificationFilter,
  PagedResultDto
} from '@/lib/types/notification';

class NotificationService {
  /**
   * Get notifications with filters
   */
  async getNotifications(filter: NotificationFilter): Promise<PagedResultDto<Notification>> {
    // Create a new URLSearchParams object
    const searchParams = new URLSearchParams();
    
    // Add page and pageSize parameters
    if (filter.page) searchParams.append('page', filter.page.toString());
    if (filter.pageSize) searchParams.append('pageSize', filter.pageSize.toString());
    
    // Add type parameter if specified
    if (filter.type) {
      searchParams.append('type', filter.type);
    }
    
    // Add date parameters in the correct format if they exist
    if (filter.startDate) {
      const startDate = new Date(filter.startDate).toISOString();
      searchParams.append('startDate', startDate);
    }
    
    if (filter.endDate) {
      const endDate = new Date(filter.endDate).toISOString();
      searchParams.append('endDate', endDate);
    }
    
    // Make the request with the formatted query string
    const response = await axiosInstance.get<PagedResultDto<Notification>>(`/Notification?${searchParams.toString()}`);
    return response.data;
  }

  /**
   * Get notification by ID
   */
  async getNotificationById(id: string): Promise<Notification> {
    const response = await axiosInstance.get<Notification>(`/Notification/${id}`);
    return response.data;
  }

  /**
   * Create a new notification
   */
  async createNotification(notification: NotificationCreateRequest): Promise<Notification> {
    const response = await axiosInstance.post<Notification>('/Notification', notification);
    return response.data;
  }

  /**
   * Delete notification by ID
   */
  async deleteNotification(id: string): Promise<void> {
    await axiosInstance.delete(`/Notification/${id}`);
  }
}

export default new NotificationService(); 