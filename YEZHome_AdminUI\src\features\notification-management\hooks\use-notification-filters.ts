import { useState } from 'react';
import type { NotificationFilter, NotificationType } from '@/lib/types/notification';

export const useNotificationFilters = () => {
  const [filters, setFilters] = useState<NotificationFilter>({
    page: 1,
    pageSize: 50,
    type: undefined,
    startDate: undefined,
    endDate: undefined
  });

  // Functions to update individual filters
  const updateType = (type: NotificationType | undefined) => {
    setFilters(prev => ({ ...prev, type, page: 1 }));
  };

  const updateDateRange = (startDate: string | undefined, endDate: string | undefined) => {
    setFilters(prev => ({ ...prev, startDate, endDate, page: 1 }));
  };

  const updatePage = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const updatePageSize = (pageSize: number) => {
    setFilters(prev => ({ ...prev, pageSize, page: 1 }));
  };

  const resetFilters = () => {
    setFilters({
      page: 1,
      pageSize: 50,
      type: undefined,
      startDate: undefined,
      endDate: undefined
    });
  };

  return {
    filters,
    updateType,
    updateDateRange,
    updatePage,
    updatePageSize,
    resetFilters,
    setFilters
  };
}; 