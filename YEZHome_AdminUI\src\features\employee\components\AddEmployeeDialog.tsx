import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query'
import { Plus } from 'lucide-react'
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle, DialogFooter, DialogTrigger } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Form } from '@/components/ui/form'
import { Spinner } from '@/components/ui/spinner'
import { toast } from '@/components/ui/use-toast'
import userService from '@/services/user-service'
import { type CreateEmployeeFormData, createEmployeeWithSingleRoleSchema } from '@/lib/types/employee'

interface AddEmployeeDialogProps {
  onSuccess?: () => void
}

export const AddEmployeeDialog = ({ onSuccess }: AddEmployeeDialogProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const queryClient = useQueryClient()

  const form = useForm<CreateEmployeeFormData>({
    resolver: zodResolver(createEmployeeWithSingleRoleSchema),
    mode: 'onChange',
    defaultValues: {
      fullName: '',
      email: '',
      password: '',
      phone: '',
      roleId: '',
    },
  })

  // Fetch roles
  const { data: roles, isLoading: isLoadingRoles } = useQuery({
    queryKey: ['roles'],
    queryFn: () => userService.getRoles(),
    enabled: isOpen,
  })

  const createEmployeeMutation = useMutation({
    mutationFn: userService.createEmployee,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] })
      setIsOpen(false)
      form.reset()
      onSuccess?.()
      toast({
        title: "Thành công",
        description: "Đã thêm nhân viên mới",
        variant: "success",
      })
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: "Không thể thêm nhân viên. Vui lòng thử lại.",
        variant: "destructive",
      })
      console.error('Error creating employee:', error)
    }
  })

  const onSubmit = (data: CreateEmployeeFormData) => {
    const apiData = {
      fullName: data.fullName,
      email: data.email,
      password: data.password,
      phone: data.phone,
      roleIds: [data.roleId]
    }

    createEmployeeMutation.mutate(apiData)
  }

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open)
    if (!open) {
      form.reset()
    }
  }

  console.log('Phone error:', form.formState.errors.phone)

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Thêm nhân viên
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Thêm nhân viên mới</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="fullName">Họ tên</Label>
              <Input
                id="fullName"
                placeholder="Nhập họ tên"
                {...form.register('fullName')}
              />
              {form.formState.errors.fullName && (
                <p className="text-sm text-red-500">{form.formState.errors.fullName.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="Nhập email"
                {...form.register('email')}
              />
              {form.formState.errors.email && (
                <p className="text-sm text-red-500">{form.formState.errors.email.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Mật khẩu</Label>
              <Input
                id="password"
                type="password"
                placeholder="Nhập mật khẩu"
                {...form.register('password')}
              />
              {form.formState.errors.password && (
                <p className="text-sm text-red-500">{form.formState.errors.password.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">Số điện thoại</Label>
              <Input
                id="phone"
                placeholder="Nhập số điện thoại"
                {...form.register('phone')}
              />
              {form.formState.errors.phone && (
                <p className="text-sm text-red-500">
                  {form.formState.errors.phone.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label>Vai trò</Label>
              {isLoadingRoles ? (
                <div className="flex items-center justify-center py-2">
                  <Spinner className="h-4 w-4 mr-2" />
                  <span className="text-sm">Đang tải vai trò...</span>
                </div>
              ) : roles && roles.length > 0 ? (
                <select
                  className="w-full p-2 border rounded-md"
                  {...form.register('roleId')}
                >
                  <option value="">-- Chọn vai trò --</option>
                  {roles.map((role) => (
                    <option key={role.id} value={role.id}>
                      {role.roleName}
                    </option>
                  ))}
                </select>
              ) : (
                <p className="text-sm text-muted-foreground">Không có vai trò nào</p>
              )}
              {form.formState.errors.roleId && (
                <p className="text-sm text-red-500">{form.formState.errors.roleId.message}</p>
              )}
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                Thoát
              </Button>
              <Button type="submit" disabled={createEmployeeMutation.isPending}>
                {createEmployeeMutation.isPending ? (
                  <>
                    <Spinner className="mr-2 h-4 w-4" />
                    Đang thêm...
                  </>
                ) : (
                  'Thêm'
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
} 