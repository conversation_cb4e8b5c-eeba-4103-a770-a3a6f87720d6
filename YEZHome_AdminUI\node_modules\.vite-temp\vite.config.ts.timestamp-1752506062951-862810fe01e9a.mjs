// vite.config.ts
import { defineConfig } from "file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_AdminUI/node_modules/vite/dist/node/index.js";
import react from "file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_AdminUI/node_modules/@vitejs/plugin-react/dist/index.mjs";
import tailwindcss from "file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_AdminUI/node_modules/@tailwindcss/vite/dist/index.mjs";
import { tanstackRouter } from "file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_AdminUI/node_modules/@tanstack/router-plugin/dist/esm/vite.js";
import basicSsl from "file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_AdminUI/node_modules/@vitejs/plugin-basic-ssl/dist/index.mjs";
import path from "path";
var __vite_injected_original_dirname = "D:\\Project\\YEZ_Tech\\YEZ_Home\\YEZHome_AdminUI";
var vite_config_default = defineConfig({
  plugins: [
    tanstackRouter({
      target: "react",
      autoCodeSplitting: true
    }),
    basicSsl(),
    react(),
    tailwindcss()
  ],
  resolve: {
    alias: {
      "@": path.resolve(__vite_injected_original_dirname, "./src")
    }
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcudHMiXSwKICAic291cmNlUm9vdCI6ICJEOlxcUHJvamVjdFxcWUVaX1RlY2hcXFlFWl9Ib21lXFxZRVpIb21lX0FkbWluVUlcXCIsCiAgInNvdXJjZXNDb250ZW50IjogWyJjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZGlybmFtZSA9IFwiRDpcXFxcUHJvamVjdFxcXFxZRVpfVGVjaFxcXFxZRVpfSG9tZVxcXFxZRVpIb21lX0FkbWluVUlcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZmlsZW5hbWUgPSBcIkQ6XFxcXFByb2plY3RcXFxcWUVaX1RlY2hcXFxcWUVaX0hvbWVcXFxcWUVaSG9tZV9BZG1pblVJXFxcXHZpdGUuY29uZmlnLnRzXCI7Y29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2ltcG9ydF9tZXRhX3VybCA9IFwiZmlsZTovLy9EOi9Qcm9qZWN0L1lFWl9UZWNoL1lFWl9Ib21lL1lFWkhvbWVfQWRtaW5VSS92aXRlLmNvbmZpZy50c1wiO2ltcG9ydCB7IGRlZmluZUNvbmZpZyB9IGZyb20gJ3ZpdGUnXG5pbXBvcnQgcmVhY3QgZnJvbSAnQHZpdGVqcy9wbHVnaW4tcmVhY3QnXG5pbXBvcnQgdGFpbHdpbmRjc3MgZnJvbSAnQHRhaWx3aW5kY3NzL3ZpdGUnXG5cbmltcG9ydCB7IHRhbnN0YWNrUm91dGVyICB9IGZyb20gJ0B0YW5zdGFjay9yb3V0ZXItcGx1Z2luL3ZpdGUnXG5pbXBvcnQgYmFzaWNTc2wgZnJvbSAnQHZpdGVqcy9wbHVnaW4tYmFzaWMtc3NsJztcbmltcG9ydCBwYXRoIGZyb20gJ3BhdGgnO1xuXG4vLyBodHRwczovL3ZpdGVqcy5kZXYvY29uZmlnL1xuZXhwb3J0IGRlZmF1bHQgZGVmaW5lQ29uZmlnKHtcbiAgcGx1Z2luczogW1xuICAgIHRhbnN0YWNrUm91dGVyKHtcbiAgICAgIHRhcmdldDogJ3JlYWN0JyxcbiAgICAgIGF1dG9Db2RlU3BsaXR0aW5nOiB0cnVlLFxuICAgIH0pLFxuICAgIGJhc2ljU3NsKCksXG4gICAgcmVhY3QoKSxcbiAgICB0YWlsd2luZGNzcygpLFxuICBdLFxuICByZXNvbHZlOiB7XG4gICAgYWxpYXM6IHtcbiAgICAgICdAJzogcGF0aC5yZXNvbHZlKF9fZGlybmFtZSwgJy4vc3JjJyksXG4gICAgfSxcbiAgfSxcbn0pXG4iXSwKICAibWFwcGluZ3MiOiAiO0FBQWdVLFNBQVMsb0JBQW9CO0FBQzdWLE9BQU8sV0FBVztBQUNsQixPQUFPLGlCQUFpQjtBQUV4QixTQUFTLHNCQUF1QjtBQUNoQyxPQUFPLGNBQWM7QUFDckIsT0FBTyxVQUFVO0FBTmpCLElBQU0sbUNBQW1DO0FBU3pDLElBQU8sc0JBQVEsYUFBYTtBQUFBLEVBQzFCLFNBQVM7QUFBQSxJQUNQLGVBQWU7QUFBQSxNQUNiLFFBQVE7QUFBQSxNQUNSLG1CQUFtQjtBQUFBLElBQ3JCLENBQUM7QUFBQSxJQUNELFNBQVM7QUFBQSxJQUNULE1BQU07QUFBQSxJQUNOLFlBQVk7QUFBQSxFQUNkO0FBQUEsRUFDQSxTQUFTO0FBQUEsSUFDUCxPQUFPO0FBQUEsTUFDTCxLQUFLLEtBQUssUUFBUSxrQ0FBVyxPQUFPO0FBQUEsSUFDdEM7QUFBQSxFQUNGO0FBQ0YsQ0FBQzsiLAogICJuYW1lcyI6IFtdCn0K
