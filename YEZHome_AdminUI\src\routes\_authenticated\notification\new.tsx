import { createFileRoute } from '@tanstack/react-router';
import { NewNotificationPage } from '@/features/notification-management/components/NewNotificationPage';
import { Loading } from '@/components/ui/loading';

export const Route = createFileRoute('/_authenticated/notification/new')({
  component: NewNotificationPage,
  beforeLoad: () => {
    return {
      getTitle: () => 'Tạo thông báo mới'
    };
  },
  pendingComponent: () => <Loading />,
  errorComponent: () => <div className="p-10"><PERSON><PERSON> x<PERSON>y ra lỗi khi tải dữ liệu.</div>,
}); 