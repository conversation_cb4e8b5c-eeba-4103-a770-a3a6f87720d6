{
  "JWT": {
    "Key": "3b738dea24059f653f6893317404f3ce9d90daa8aa9412eafb061ce88058545b013297f9821a54cec1fa4132622e784f22e44c75bdd781cafd045169b216334b",
    "Issuer": "https://localhost:7057/",
    "Audience": "https://localhost:3000/",
    "AccessTokenExpiration": 15,
    "RefreshTokenExpiration": 7
  },
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft.AspNetCore": "Debug",
      "Microsoft.EntityFrameworkCore": "Error",
      "RealEstate.InternalAPI.Controllers": "Debug"
    },
    "Console": {
      "IncludeScopes": true,
      "TimestampFormat": "yyyy-MM-dd HH:mm:ss "
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "YezHomeConnection": "Host=localhost;Port=5432;Database=yezhome;Username=postgres;Password=******"
  },
  "Cors": {
    "AllowedOrigins": [
      "https://localhost:4000"
    ]
  },
  "AWS": {
    "AccessKey": "********************",
    "SecretKey": "SWPLEhCIHkMZqz31pPoeSh5FS3QelDALiwoUyjct",
    "region": "ap-southeast-1",
  }
}
