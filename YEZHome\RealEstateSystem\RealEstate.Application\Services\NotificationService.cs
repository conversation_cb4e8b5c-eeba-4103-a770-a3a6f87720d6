﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using RealEstate.Application.DTO;
using RealEstate.Application.DTO.Notification;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Common;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;

namespace RealEstate.Application.Services
{
    public class NotificationService : INotificationService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public NotificationService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        public async Task<NotificationDto> GetNotificationByIdAsync(Guid id)
        {
            var notification = await _unitOfWork.Notifications.GetByIdAsync(id);
            return _mapper.Map<NotificationDto>(notification);
        }

        public async Task<bool> MarkAsReadAsync(Guid id, Guid userId)
        {
            var notification = await _unitOfWork.Notifications.GetByIdAsync(id, asNoTracking: false);
            
            if (notification == null)
                return false;
                
            // Verify the notification belongs to this user or is a system/promotion notification
            if (notification.UserId.HasValue && notification.UserId.Value != userId)
                return false;
                
            notification.IsRead = true;
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteNotificationAsync(Guid id, Guid userId)
        {
            var notification = await _unitOfWork.Notifications.GetByIdAsync(id);
            
            if (notification == null)
                return false;
                
            // Only allow deletion if the notification belongs to this user
            if (notification.UserId.HasValue && notification.UserId.Value != userId)
                return false;
                
            _unitOfWork.Notifications.Remove(notification);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }
        
        public async Task<PagedResultDto<NotificationDto>> GetNotificationsForUserAsync(
            Guid userId, 
            DateTime? fromDate = null, 
            DateTime? toDate = null, 
            int page = 1, 
            int pageSize = 50)
        {
            var query = _unitOfWork.Notifications.GetQueryable()
                .Where(n => n.UserId == userId);
                
            // Apply date filters
            query = ApplyDateFilters(query, fromDate, toDate);
            
            // Get total count for pagination
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var notifications = await query
                .OrderByDescending(n => n.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .AsNoTracking()
                .ToListAsync();
                
            // Map to DTOs
            var notificationDtos = _mapper.Map<List<NotificationDto>>(notifications);
            
            // Return paged result
            return new PagedResultDto<NotificationDto>
            {
                Items = notificationDtos,
                TotalCount = totalCount,
                PageCount = (int)Math.Ceiling(totalCount / (double)pageSize),
                CurrentPage = page,
                PageSize = pageSize
            };
        }
        
        public async Task<PagedResultDto<NotificationDto>> GetNotificationsByTypeAsync(
            string? type = null, 
            DateTime? fromDate = null, 
            DateTime? toDate = null, 
            int page = 1, 
            int pageSize = 50,
            bool getOnlySystem = true)
        {
            var query = _unitOfWork.Notifications.GetQueryable();

            if (type != null)
            {
                var normalizedType = type.ToLower();

                query = normalizedType switch
                {
                    "system" => query.Where(n => n.Type == EnumValues.NotificationType.System.ToString()),

                    "promotion" => query.Where(n =>
                        n.Type == EnumValues.NotificationType.Promotion.ToString() ||
                        n.Type == EnumValues.NotificationType.News.ToString()),

                    "transaction" when !getOnlySystem => query.Where(n =>
                        n.Type == EnumValues.NotificationType.Transaction.ToString() ||
                        n.Type == EnumValues.NotificationType.WalletUpdate.ToString()),

                    "contact" when !getOnlySystem => query.Where(n =>
                        n.Type == EnumValues.NotificationType.Contact.ToString()),

                    _ => query.Where(n => n.Type == type) // fallback
                };
            }
            else
            {
                if (getOnlySystem)
                {
                    query = query.Where(n => n.Type == EnumValues.NotificationType.System.ToString()
                    || n.Type == EnumValues.NotificationType.Promotion.ToString()
                    || n.Type == EnumValues.NotificationType.News.ToString());
                }
            }

                // Apply date filters
                query = ApplyDateFilters(query, fromDate, toDate);
            
            // Get total count for pagination
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var notifications = await query
                .OrderByDescending(n => n.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .AsNoTracking()
                .ToListAsync();
                
            // Map to DTOs
            var notificationDtos = _mapper.Map<List<NotificationDto>>(notifications);
            
            // Return paged result
            return new PagedResultDto<NotificationDto>
            {
                Items = notificationDtos,
                TotalCount = totalCount,
                PageCount = (int)Math.Ceiling(totalCount / (double)pageSize),
                CurrentPage = page,
                PageSize = pageSize
            };
        }
        
        public async Task<PagedResultDto<NotificationDto>> GetNotificationsByTypeAndUserAsync(
            string type, 
            Guid userId, 
            DateTime? fromDate = null, 
            DateTime? toDate = null, 
            int page = 1, 
            int pageSize = 50)
        {
            var query = _unitOfWork.Notifications.GetQueryable()
                .Where(n => n.UserId == userId);
                
            // Filter by notification type based on the requested group
            query = type.ToLower() switch
            {
                "system" => query.Where(n => n.Type == EnumValues.NotificationType.System.ToString()),
                "transaction" => query.Where(n => n.Type == EnumValues.NotificationType.Transaction.ToString() || n.Type == EnumValues.NotificationType.WalletUpdate.ToString()),
                "promotion" => query.Where(n => n.Type == EnumValues.NotificationType.Promotion.ToString() || n.Type == EnumValues.NotificationType.News.ToString()),
                "contact" => query.Where(n => n.Type == EnumValues.NotificationType.Contact.ToString()),
                _ => query.Where(n => n.Type == type) // Default case for any other type
            };
                
            // Apply date filters
            query = ApplyDateFilters(query, fromDate, toDate);
            
            // Get total count for pagination
            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var notifications = await query
                .OrderByDescending(n => n.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .AsNoTracking()
                .ToListAsync();
                
            // Map to DTOs
            var notificationDtos = _mapper.Map<List<NotificationDto>>(notifications);
            
            // Return paged result
            return new PagedResultDto<NotificationDto>
            {
                Items = notificationDtos,
                TotalCount = totalCount,
                PageCount = (int)Math.Ceiling(totalCount / (double)pageSize),
                CurrentPage = page,
                PageSize = pageSize
            };
        }
        
        public async Task<PagedResultDto<NotificationDto>> GetAllNotificationsForUserAsync(
            Guid userId, 
            DateTime? fromDate = null, 
            DateTime? toDate = null, 
            int page = 1, 
            int pageSize = 50)
        {
            // Get user-specific notifications and system/promotion notifications
            var query = _unitOfWork.Notifications.GetQueryable()
                .Where(n => (n.UserId == userId) || 
                           (n.UserId == null && (n.Type == "system" || n.Type == "promotion")));
                
            // Apply date filters
            query = ApplyDateFilters(query, fromDate, toDate);
            
            // Get total count for pagination
            var totalCount = await query.CountAsync();
            
            // Apply pagination with ordering
            var notifications = await query
                .OrderByDescending(n => n.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
                
            // Map to DTOs
            var notificationDtos = _mapper.Map<List<NotificationDto>>(notifications);
            
            // Return paged result
            return new PagedResultDto<NotificationDto>
            {
                Items = notificationDtos,
                TotalCount = totalCount,
                PageCount = (int)Math.Ceiling(totalCount / (double)pageSize),
                CurrentPage = page,
                PageSize = pageSize
            };
        }
        
        public async Task MarkAllAsReadAsync(Guid userId)
        {
            // Get all unread notifications for this user (including system/promotion)
            var notifications = await _unitOfWork.Notifications.GetQueryable()
                .Where(n => !n.IsRead && 
                          ((n.UserId == userId) || 
                           (n.UserId == null && (n.Type == "system" || n.Type == "promotion"))))
                .ToListAsync();
                
            // Mark all as read
            foreach (var notification in notifications)
            {
                notification.IsRead = true;
            }
            
            await _unitOfWork.SaveChangesAsync();
        }
        
        public async Task<int> GetUnreadNotificationCountAsync(Guid userId)
        {
            // Count unread notifications for this user (including system/promotion)
            return await _unitOfWork.Notifications.GetQueryable().AsNoTracking()
                .CountAsync(n => !n.IsRead && 
                              ((n.UserId == userId) || 
                               (n.UserId == null && (n.Type == "system" || n.Type == "promotion"))));
        }
        
        public async Task<NotificationDto> CreateNotificationAsync(CreateNotificationDto notificationDto)
        {
            var notification = new Notification
            {
                UserId = notificationDto.UserId,
                Type = notificationDto.Type ?? string.Empty,
                Title = notificationDto.Title ?? string.Empty,
                Message = notificationDto.Message ?? string.Empty,
                RelatedEntityId = notificationDto.RelatedEntityId,
                RelatedPropertyId = notificationDto.RelatedPropertyId,
                ActionUrl = notificationDto.ActionUrl,
                IsRead = false,
                CreatedAt = DateTime.UtcNow
            };

            await _unitOfWork.Notifications.AddAsync(notification);
            await _unitOfWork.SaveChangesAsync();

            return _mapper.Map<NotificationDto>(notification);
        }
        
        // Legacy method for compatibility
        public async Task<IEnumerable<NotificationDto>> GetAllNotificationsAsync(Guid? userId)
        {
            IEnumerable<Notification> notifications;
            
            if (userId.HasValue)
            {
                // Get user-specific notifications and system/promotion notifications
                notifications = await _unitOfWork.Notifications.GetQueryable()
                    .Where(n => (n.UserId == userId) || 
                               (n.UserId == null && (n.Type == "system" || n.Type == "promotion")))
                    .OrderByDescending(n => n.CreatedAt)
                    .ToListAsync();
            }
            else
            {
                // Get all notifications (admin view)
                notifications = await _unitOfWork.Notifications.GetQueryable()
                    .OrderByDescending(n => n.CreatedAt)
                    .ToListAsync();
            }
            
            return _mapper.Map<IEnumerable<NotificationDto>>(notifications);
        }
        
        // Helper method to apply date filters
        private IQueryable<Notification> ApplyDateFilters(
            IQueryable<Notification> query, 
            DateTime? fromDate, 
            DateTime? toDate)
        {
            if (fromDate.HasValue)
            {
                query = query.Where(n => n.CreatedAt >= fromDate.Value);
            }
            
            if (toDate.HasValue)
            {
                // Add one day to include the end date fully
                var endDate = toDate.Value.AddDays(1).AddTicks(-1);
                query = query.Where(n => n.CreatedAt <= endDate);
            }
            
            return query.AsNoTracking();
        }

        /// <summary>
        /// Helper method to construct action URLs for different notification types
        /// </summary>
        public static string ConstructActionUrl(string notificationType, Guid? propertyId = null, Guid? contactRequestId = null, Guid? transactionId = null)
        {
            return notificationType.ToLower() switch
            {
                "contact" when propertyId.HasValue => $"/bds/{propertyId}",
                "contact" when contactRequestId.HasValue => $"/dashboard/contact-requests/{contactRequestId}",
                "transaction" when transactionId.HasValue => $"/dashboard/transactions/{transactionId}",
                "walletupdate" => "/dashboard/wallet",
                "system" => "/dashboard/notifications",
                "promotion" => "/promotions",
                "news" => "/news",
                _ => "/dashboard/notifications"
            };
        }
    }
}
