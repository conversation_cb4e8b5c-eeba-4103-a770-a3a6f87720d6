import{c as E,b as q,r as y,j as e,B as d,L as B,X,a as K,k as Q,l as _,m as $}from"./index-DFXNGaQ4.js";import{C as D,a as k,b as I,d as P}from"./card-Dikj3j-6.js";import{c as U,u as W,h as Y,i as G,T as J,a as Z,b,d as ee,f as M,e as ae,g as O}from"./table-BxBNKVGC.js";import{n as C,a as L}from"./notification-BvCoxbDh.js";import{B as w}from"./badge-BmdoMzu9.js";import{D as se,a as te,E as ne,b as le,c as R}from"./dropdown-menu-DMTwZ3V-.js";import{E as ie}from"./eye-DjtIFTil.js";import{T as re}from"./trash-2-BZu0p0bP.js";import{P as F,a as A,b as H}from"./popover-CUilP8_A.js";import{C as V}from"./chevron-down-DuS99v9n.js";import{I as z}from"./input-D3ozjVi3.js";import{L as f}from"./label-BscdIpbp.js";import{C as ce}from"./calendar-CjeZn9Ho.js";import{A as oe,a as de,b as he,c as me,d as ge,e as xe,f as pe,g as ue}from"./alert-dialog-CINOfzqV.js";import{P as je}from"./plus-DZShkg7N.js";import"./index-BpksVOb4.js";/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ne=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],ve=E("RefreshCw",Ne),ye=({filters:c,onPageChange:i,onViewDetails:m,onDelete:h})=>{const{data:s,isLoading:o}=q({queryKey:["notifications",c],queryFn:()=>L.getNotifications(c)}),l=U(),r=y.useMemo(()=>[l.accessor("type",{header:"Loại",cell:a=>{const t=a.getValue(),x=j(t);return e.jsx("div",{className:"w-24",children:e.jsx(w,{variant:"subtle",colorScheme:x,className:"whitespace-nowrap overflow-hidden text-ellipsis max-w-full",showDot:!0,dotColor:x,title:C[t]||t,children:C[t]||t})})}}),l.accessor("title",{header:"Tiêu đề",cell:a=>e.jsx("div",{className:"max-w-48 truncate font-medium",title:a.getValue(),children:a.getValue()})}),l.accessor("message",{header:"Nội dung",cell:a=>e.jsx("div",{className:"max-w-64 truncate text-sm text-gray-600",title:a.getValue(),children:a.getValue()})}),l.accessor("userId",{header:"Người nhận",cell:a=>e.jsx("div",{className:"text-sm text-gray-600",children:a.getValue()||"-"})}),l.accessor("actionUrl",{header:"URL hành động",cell:a=>{const t=a.getValue();return t?e.jsx("a",{href:t,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800 text-sm truncate max-w-32 block",title:t,children:t}):e.jsx("span",{className:"text-gray-400 text-sm",children:"-"})}}),l.accessor("createdAt",{header:"Ngày tạo",cell:a=>new Date(a.getValue()).toLocaleDateString("vi-VN")}),l.display({id:"actions",header:"",cell:a=>{const t=a.row.original.id;return e.jsx("div",{className:"text-right",children:e.jsxs(se,{children:[e.jsx(te,{asChild:!0,children:e.jsx(d,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:e.jsx(ne,{className:"h-4 w-4"})})}),e.jsxs(le,{align:"end",children:[e.jsxs(R,{onClick:()=>m(t),children:[e.jsx(ie,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:"Xem chi tiết"})]}),e.jsxs(R,{onClick:()=>h(t),className:"text-red-600",children:[e.jsx(re,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:"Xóa"})]})]})]})})}})],[l,m,h]),n=W({data:(s==null?void 0:s.items)||[],columns:r,getCoreRowModel:G(),getPaginationRowModel:Y(),pageCount:(s==null?void 0:s.pageCount)||-1,manualPagination:!0,state:{pagination:{pageIndex:(c.page||1)-1,pageSize:c.pageSize||10}},onPaginationChange:a=>{if(typeof a=="function"){const t=a(n.getState().pagination);i(t.pageIndex+1)}else i(a.pageIndex+1)}}),j=a=>{switch(a){case"SYSTEM":return"blue";case"PROMOTION":return"orange";case"NEWS":return"green";default:return"gray"}};return o?e.jsx(B,{}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"border rounded-md",children:e.jsxs(J,{className:"w-full caption-bottom text-sm",children:[e.jsx(Z,{className:"bg-muted[&_tr]:border-b bg-muted sticky top-0 z-10",children:n.getHeaderGroups().map(a=>e.jsx(b,{children:a.headers.map(t=>e.jsx(ee,{children:t.isPlaceholder?null:M(t.column.columnDef.header,t.getContext())},t.id))},a.id))}),e.jsx(ae,{children:n.getRowModel().rows.length?n.getRowModel().rows.map(a=>e.jsx(b,{children:a.getVisibleCells().map(t=>e.jsx(O,{className:"whitespace-normal",children:M(t.column.columnDef.cell,t.getContext())},t.id))},a.id)):e.jsx(b,{children:e.jsx(O,{colSpan:r.length,className:"h-24 text-center",children:"Không có dữ liệu"})})})]})}),e.jsxs("div",{className:"flex items-center justify-between py-4",children:[e.jsxs("div",{className:"text-sm text-muted-foreground",children:["Hiển thị ",n.getState().pagination.pageIndex*n.getState().pagination.pageSize+1," đến"," ",Math.min((n.getState().pagination.pageIndex+1)*n.getState().pagination.pageSize,(s==null?void 0:s.totalCount)||0)," ","trong tổng số ",(s==null?void 0:s.totalCount)||0," kết quả"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(d,{variant:"outline",size:"sm",onClick:()=>n.previousPage(),disabled:!n.getCanPreviousPage(),children:"Trước"}),e.jsxs("span",{className:"text-sm",children:["Trang ",n.getState().pagination.pageIndex+1," / ",(s==null?void 0:s.pageCount)||1]}),e.jsx(d,{variant:"outline",size:"sm",onClick:()=>n.nextPage(),disabled:!n.getCanNextPage(),children:"Sau"})]})]})]})},fe=({selectedType:c,onTypeChange:i})=>{const[m,h]=y.useState(!1),s=Object.entries(C).map(([l,r])=>({value:l,label:r})),o=l=>{i(l),h(!1)};return e.jsxs(F,{open:m,onOpenChange:h,children:[e.jsx(A,{asChild:!0,children:e.jsxs(d,{variant:"outline",className:"gap-2",children:["Loại thông báo",c&&e.jsx(w,{variant:"secondary",className:"ml-1 px-1 py-0",children:"1"}),e.jsx(V,{className:"h-4 w-4"})]})}),e.jsx(H,{className:"w-64 p-4",align:"start",children:e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"font-medium",children:"Loại thông báo"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("button",{onClick:()=>o(void 0),className:`w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 ${c?"":"bg-blue-50 text-blue-700"}`,children:"Tất cả"}),s.map(l=>e.jsx("button",{onClick:()=>o(l.value),className:`w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 ${c===l.value?"bg-blue-50 text-blue-700":""}`,children:l.label},l.value))]})]})})]})},be=({startDate:c,endDate:i,onDateRangeChange:m})=>{const[h,s]=y.useState(!1),[o,l]=y.useState(c||""),[r,n]=y.useState(i||""),j=c||i,a=()=>{m(o||void 0,r||void 0),s(!1)},t=()=>{l(""),n(""),m(void 0,void 0),s(!1)},x=N=>{const g=new Date;let p,u;switch(N){case"today":p=g.toISOString().split("T")[0],u=g.toISOString().split("T")[0];break;case"last7days":p=new Date(g.getTime()-7*24*60*60*1e3).toISOString().split("T")[0],u=g.toISOString().split("T")[0];break;case"last30days":p=new Date(g.getTime()-30*24*60*60*1e3).toISOString().split("T")[0],u=g.toISOString().split("T")[0];break;case"last90days":p=new Date(g.getTime()-90*24*60*60*1e3).toISOString().split("T")[0],u=g.toISOString().split("T")[0];break}p&&u&&(l(p),n(u),m(p,u),s(!1))};return e.jsxs(F,{open:h,onOpenChange:s,children:[e.jsx(A,{asChild:!0,children:e.jsxs(d,{variant:"outline",className:"gap-2",children:[e.jsx(ce,{className:"h-4 w-4"}),"Khoảng thời gian",j&&e.jsx(w,{variant:"secondary",className:"ml-1 px-1 py-0",children:"1"}),e.jsx(V,{className:"h-4 w-4"})]})}),e.jsx(H,{className:"w-80 p-4",align:"start",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h4",{className:"font-medium",children:"Khoảng thời gian"}),j&&e.jsxs(d,{variant:"ghost",size:"sm",onClick:t,className:"h-6 px-2 text-xs",children:[e.jsx(X,{className:"h-3 w-3 mr-1"}),"Xóa"]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(f,{className:"text-xs font-medium text-gray-600",children:"Tùy chọn nhanh"}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsx(d,{variant:"outline",size:"sm",onClick:()=>x("today"),className:"text-xs",children:"Hôm nay"}),e.jsx(d,{variant:"outline",size:"sm",onClick:()=>x("last7days"),className:"text-xs",children:"7 ngày qua"}),e.jsx(d,{variant:"outline",size:"sm",onClick:()=>x("last30days"),className:"text-xs",children:"30 ngày qua"}),e.jsx(d,{variant:"outline",size:"sm",onClick:()=>x("last90days"),className:"text-xs",children:"90 ngày qua"})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(f,{className:"text-xs font-medium text-gray-600",children:"Tùy chọn thời gian"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{children:[e.jsx(f,{htmlFor:"start-date",className:"text-sm",children:"Từ ngày"}),e.jsx(z,{id:"start-date",type:"date",value:o,onChange:N=>l(N.target.value),className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx(f,{htmlFor:"end-date",className:"text-sm",children:"Đến ngày"}),e.jsx(z,{id:"end-date",type:"date",value:r,onChange:N=>n(N.target.value),className:"mt-1"})]})]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(d,{onClick:a,className:"flex-1",children:"Áp dụng"}),e.jsx(d,{variant:"outline",onClick:()=>s(!1),className:"flex-1",children:"Hủy"})]})]})})]})},Ce=()=>{const[c,i]=y.useState({page:1,pageSize:10,type:void 0,startDate:void 0,endDate:void 0});return{filters:c,updateType:r=>{i(n=>({...n,type:r,page:1}))},updateDateRange:(r,n)=>{i(j=>({...j,startDate:r,endDate:n,page:1}))},updatePage:r=>{i(n=>({...n,page:r}))},updatePageSize:r=>{i(n=>({...n,pageSize:r,page:1}))},resetFilters:()=>{i({page:1,pageSize:10,type:void 0,startDate:void 0,endDate:void 0})},setFilters:i}},we=()=>{const c=K(),{toast:i}=Q(),m=_(),[h,s]=y.useState(null),{filters:o,updateType:l,updateDateRange:r,updatePage:n,resetFilters:j}=Ce(),a=$({mutationFn:v=>L.deleteNotification(v),onSuccess:()=>{m.invalidateQueries({queryKey:["notifications"]}),i({title:"Thành công",description:"Thông báo đã được xóa thành công"}),s(null)},onError:v=>{var S,T;i({title:"Lỗi",description:((T=(S=v.response)==null?void 0:S.data)==null?void 0:T.message)||"Có lỗi xảy ra khi xóa thông báo",variant:"destructive"}),s(null)}}),t=()=>{c({to:"/notification/new"})},x=v=>{c({to:"/notification/$notificationId",params:{notificationId:v}})},N=v=>{s(v)},g=()=>{h&&a.mutate(h)},p=()=>{j()},u=o.type||o.startDate||o.endDate;return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Quản lý thông báo"}),e.jsx("p",{className:"text-gray-600 mt-1",children:"Quản lý và tạo thông báo cho người dùng"})]}),e.jsxs(d,{onClick:t,className:"gap-2",children:[e.jsx(je,{className:"h-4 w-4"}),"Tạo thông báo"]})]}),e.jsxs(D,{children:[e.jsx(k,{children:e.jsx(I,{className:"text-lg",children:"Bộ lọc"})}),e.jsx(P,{children:e.jsxs("div",{className:"flex flex-wrap gap-3 items-center",children:[e.jsx(fe,{selectedType:o.type,onTypeChange:l}),e.jsx(be,{startDate:o.startDate,endDate:o.endDate,onDateRangeChange:r}),u&&e.jsxs(d,{variant:"outline",onClick:p,className:"gap-2",children:[e.jsx(ve,{className:"h-4 w-4"}),"Xóa bộ lọc"]})]})})]}),e.jsxs(D,{children:[e.jsx(k,{children:e.jsx(I,{children:"Danh sách thông báo"})}),e.jsx(P,{children:e.jsx(ye,{filters:o,onPageChange:n,onViewDetails:x,onDelete:N})})]}),e.jsx(oe,{open:!!h,onOpenChange:()=>s(null),children:e.jsxs(de,{children:[e.jsxs(he,{children:[e.jsx(me,{children:"Xác nhận xóa"}),e.jsx(ge,{children:"Bạn có chắc chắn muốn xóa thông báo này không? Hành động này không thể hoàn tác."})]}),e.jsxs(xe,{children:[e.jsx(pe,{children:"Hủy"}),e.jsx(ue,{onClick:g,className:"bg-red-600 hover:bg-red-700",children:"Xóa"})]})]})})]})},qe=we;export{qe as component};
