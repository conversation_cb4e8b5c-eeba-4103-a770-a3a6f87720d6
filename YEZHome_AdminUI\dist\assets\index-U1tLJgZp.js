import{a as pe,l as ge,V as ue,r as m,b as V,m as D,W as j,Y as x,j as e,B as c,P as ie,Z as p,O as ae,S as d,Q as je,T as G}from"./index-DFXNGaQ4.js";import{c as ve,u as fe,h as Ne,i as ye,T as Se,a as be,b as F,d as Ce,f as Y,e as we,g as J}from"./table-BxBNKVGC.js";import{C as $,d as ee,a as De,b as Te}from"./card-Dikj3j-6.js";import{I as v}from"./input-D3ozjVi3.js";import{L as g}from"./label-BscdIpbp.js";import{B as A}from"./badge-BmdoMzu9.js";import{D as L,a as M,b as K,c as O,d as R}from"./dialog-DzrnKtbI.js";import{A as Ee,a as Pe,b as ke,c as Ie,d as Ve,e as Fe,f as Ae,g as Le}from"./alert-dialog-CINOfzqV.js";import{u as se,F as ne}from"./form-lBragMB2.js";import{a as Me}from"./zod-TzqjP9W3.js";import{D as Ke,a as Oe,E as Re,b as He,c as H}from"./dropdown-menu-DMTwZ3V-.js";import{E as ze}from"./eye-DjtIFTil.js";import{S as qe}from"./square-pen-U2EKTpxQ.js";import{T as Qe}from"./trash-2-BZu0p0bP.js";import{P as Be}from"./plus-DZShkg7N.js";import{S as Ue}from"./search-Crlu67m_.js";import"./index-BpksVOb4.js";ae({page:G().optional().default(1),pageSize:G().optional().default(ie),email:d().optional().default(""),name:d().optional().default(""),phone:d().optional().default(""),sortColumn:d().optional(),sortDescending:je().optional()});const Xe=ae({fullName:d().min(1,"Họ tên không được để trống"),email:d().email("Email không hợp lệ").min(1,"Email không được để trống"),password:d().min(6,"Mật khẩu phải có ít nhất 6 ký tự"),phone:d().min(1,"Số điện thoại không được để trống"),roleId:d().min(1,"Phải chọn vai trò")}),ms=function(){var Z,_;const T=pe(),N=ge(),a=ue.useSearch(),[z,y]=m.useState(!1),[q,E]=m.useState(!1),[P,S]=m.useState(!1),[te,k]=m.useState(!1),[o,b]=m.useState(null),C=m.useMemo(()=>({PageNumber:a.page,PageSize:a.pageSize,Email:a.email||void 0,Name:a.name||void 0,Phone:a.phone||void 0,SortColumn:a.sortColumn,SortDescending:a.sortDescending}),[a]),u=se({defaultValues:{email:a.email,name:a.name,phone:a.phone}}),t=se({resolver:Me(Xe),defaultValues:{fullName:"",email:"",password:"",phone:"",roleId:""}}),{data:l,isLoading:le}=V({queryKey:["employees",C],queryFn:()=>j.getEmployees(C)}),{data:i,isLoading:Q,refetch:Ze}=V({queryKey:["employee",o==null?void 0:o.id],queryFn:()=>o!=null&&o.id?j.getUserById(o.id):null,enabled:!!(o!=null&&o.id)&&(q||P)}),{data:f,isLoading:B}=V({queryKey:["roles"],queryFn:()=>j.getRoles(),enabled:z||P}),I=D({mutationFn:j.createEmployee,onSuccess:()=>{N.invalidateQueries({queryKey:["employees"]}),y(!1),t.reset(),x({title:"Thành công",description:"Đã thêm nhân viên mới",variant:"success"})},onError:s=>{x({title:"Lỗi",description:"Không thể thêm nhân viên. Vui lòng thử lại.",variant:"destructive"}),console.error("Error creating employee:",s)}}),w=D({mutationFn:({id:s,status:n})=>j.updateUserStatus(s,{isActive:n}),onSuccess:()=>{N.invalidateQueries({queryKey:["employees"]}),S(!1),x({title:"Thành công",description:"Đã cập nhật trạng thái nhân viên",variant:"success"})},onError:s=>{x({title:"Lỗi",description:"Không thể cập nhật trạng thái nhân viên. Vui lòng thử lại.",variant:"destructive"}),console.error("Error updating employee status:",s)}}),U=D({mutationFn:s=>j.updateUserRoles(s),onSuccess:()=>{N.invalidateQueries({queryKey:["employees"]}),N.invalidateQueries({queryKey:["employee"]}),x({title:"Thành công",description:"Đã cập nhật vai trò nhân viên",variant:"success"})},onError:s=>{x({title:"Lỗi",description:"Không thể cập nhật vai trò nhân viên. Vui lòng thử lại.",variant:"destructive"}),console.error("Error updating employee roles:",s)}}),X=D({mutationFn:s=>j.deleteUser(s),onSuccess:()=>{N.invalidateQueries({queryKey:["employees"]}),k(!1),b(null),x({title:"Thành công",description:"Đã xóa nhân viên",variant:"success"})},onError:s=>{x({title:"Lỗi",description:"Không thể xóa nhân viên. Vui lòng thử lại.",variant:"destructive"}),console.error("Error deleting employee:",s)}});m.useEffect(()=>{u.reset({email:a.email,name:a.name,phone:a.phone})},[a,u]);const re=s=>{T({to:"/employee",search:{...a,...s,page:1}})},oe=s=>{const n={fullName:s.fullName,email:s.email,password:s.password,phone:s.phone,roleIds:[s.roleId]};I.mutate(n)},ce=s=>{b(s),E(!0)},de=s=>{b(s),S(!0)},he=s=>{b(s),k(!0)},me=()=>{o&&X.mutate(o.id)},h=ve(),W=m.useMemo(()=>[h.accessor("fullName",{header:"Họ tên",cell:s=>s.getValue()||"-"}),h.accessor("email",{header:"Email",cell:s=>s.getValue()||"-"}),h.accessor("phone",{header:"Số điện thoại",cell:s=>s.getValue()||"-"}),h.accessor("userType",{header:"Loại người dùng",cell:s=>s.getValue()||"-"}),h.accessor("roleObjects",{header:"Vai trò",cell:s=>{var n;return((n=s.getValue())==null?void 0:n.map(xe=>xe.roleName).join(", "))||"-"}}),h.accessor("isActive",{header:"Trạng thái",cell:s=>{const n=s.getValue();return e.jsx(A,{variant:"subtle",colorScheme:n?"green":"red",children:n?"Đang hoạt động":"Đã vô hiệu hóa"})}}),h.accessor(s=>s.lastLogin?new Date(s.lastLogin).toLocaleDateString("vi-VN"):"-",{id:"lastLogin",header:"Đăng nhập cuối",cell:s=>s.getValue()}),h.display({id:"actions",header:"",cell:s=>{const n=s.row.original;return e.jsx("div",{className:"text-right",children:e.jsxs(Ke,{children:[e.jsx(Oe,{asChild:!0,children:e.jsx(c,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:e.jsx(Re,{className:"h-4 w-4"})})}),e.jsxs(He,{align:"end",children:[e.jsxs(H,{onClick:()=>ce(n),children:[e.jsx(ze,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:"Xem chi tiết"})]}),e.jsxs(H,{onClick:()=>de(n),children:[e.jsx(qe,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:"Chỉnh sửa"})]}),e.jsxs(H,{onClick:()=>he(n),className:"text-red-600",children:[e.jsx(Qe,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:"Xóa"})]})]})]})})}})],[]),r=fe({data:(l==null?void 0:l.items)||[],columns:W,getCoreRowModel:ye(),getPaginationRowModel:Ne(),pageCount:(l==null?void 0:l.pageCount)||-1,manualPagination:!0,state:{pagination:{pageIndex:(C.PageNumber||1)-1,pageSize:C.PageSize||ie}},onPaginationChange:s=>{if(typeof s=="function"){const n=s(r.getState().pagination);T({to:"/employee",search:{...a,page:n.pageIndex+1,pageSize:n.pageSize}})}else T({to:"/employee",search:{...a,page:s.pageIndex+1,pageSize:s.pageSize}})}});return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Quản lý nhân viên"}),e.jsxs(c,{onClick:()=>y(!0),children:[e.jsx(Be,{className:"mr-2 h-4 w-4"}),"Thêm nhân viên"]})]}),e.jsx($,{children:e.jsx(ee,{children:e.jsx(ne,{...u,children:e.jsxs("form",{onSubmit:u.handleSubmit(re),className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(g,{htmlFor:"email",children:"Email"}),e.jsx(v,{id:"email",placeholder:"Nhập email",...u.register("email")})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(g,{htmlFor:"name",children:"Họ tên"}),e.jsx(v,{id:"name",placeholder:"Nhập họ tên",...u.register("name")})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(g,{htmlFor:"phone",children:"Số điện thoại"}),e.jsx(v,{id:"phone",placeholder:"Nhập số điện thoại",...u.register("phone")})]})]}),e.jsx("div",{className:"flex justify-end",children:e.jsxs(c,{type:"submit",children:[e.jsx(Ue,{className:"mr-2 h-4 w-4"}),"Tìm kiếm"]})})]})})})}),e.jsxs($,{children:[e.jsx(De,{children:e.jsx(Te,{children:"Danh sách nhân viên"})}),e.jsx(ee,{children:le?e.jsx("div",{className:"flex justify-center py-8",children:e.jsx(p,{})}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"rounded-md border",children:e.jsxs(Se,{children:[e.jsx(be,{className:"bg-muted[&_tr]:border-b bg-muted sticky top-0 z-10",children:r.getHeaderGroups().map(s=>e.jsx(F,{children:s.headers.map(n=>e.jsx(Ce,{children:n.isPlaceholder?null:Y(n.column.columnDef.header,n.getContext())},n.id))},s.id))}),e.jsx(we,{children:r.getRowModel().rows.length?r.getRowModel().rows.map(s=>e.jsx(F,{children:s.getVisibleCells().map(n=>e.jsx(J,{children:Y(n.column.columnDef.cell,n.getContext())},n.id))},s.id)):e.jsx(F,{children:e.jsx(J,{colSpan:W.length,className:"h-24 text-center",children:"Không có dữ liệu"})})})]})}),e.jsxs("div",{className:"flex items-center justify-between py-4",children:[e.jsxs("div",{className:"text-sm text-muted-foreground",children:["Hiển thị ",r.getState().pagination.pageIndex*r.getState().pagination.pageSize+1," đến"," ",Math.min((r.getState().pagination.pageIndex+1)*r.getState().pagination.pageSize,(l==null?void 0:l.totalCount)||0)," ","trong tổng số ",(l==null?void 0:l.totalCount)||0," kết quả"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(c,{variant:"outline",size:"sm",onClick:()=>r.previousPage(),disabled:!r.getCanPreviousPage(),children:"Trước"}),e.jsxs("span",{className:"text-sm",children:["Trang ",r.getState().pagination.pageIndex+1," / ",(l==null?void 0:l.pageCount)||1]}),e.jsx(c,{variant:"outline",size:"sm",onClick:()=>r.nextPage(),disabled:!r.getCanNextPage(),children:"Sau"})]})]})]})})]}),e.jsx(L,{open:z,onOpenChange:y,children:e.jsxs(M,{className:"sm:max-w-[500px]",children:[e.jsx(K,{children:e.jsx(O,{children:"Thêm nhân viên mới"})}),e.jsx(ne,{...t,children:e.jsxs("form",{onSubmit:t.handleSubmit(oe),className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(g,{htmlFor:"fullName",children:"Họ tên"}),e.jsx(v,{id:"fullName",placeholder:"Nhập họ tên",...t.register("fullName")}),t.formState.errors.fullName&&e.jsx("p",{className:"text-sm text-red-500",children:t.formState.errors.fullName.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(g,{htmlFor:"email",children:"Email"}),e.jsx(v,{id:"email",type:"email",placeholder:"Nhập email",...t.register("email")}),t.formState.errors.email&&e.jsx("p",{className:"text-sm text-red-500",children:t.formState.errors.email.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(g,{htmlFor:"password",children:"Mật khẩu"}),e.jsx(v,{id:"password",type:"password",placeholder:"Nhập mật khẩu",...t.register("password")}),t.formState.errors.password&&e.jsx("p",{className:"text-sm text-red-500",children:t.formState.errors.password.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(g,{htmlFor:"phone",children:"Số điện thoại"}),e.jsx(v,{id:"phone",placeholder:"Nhập số điện thoại",...t.register("phone")}),t.formState.errors.phone&&e.jsx("p",{className:"text-sm text-red-500",children:t.formState.errors.phone.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(g,{children:"Vai trò"}),B?e.jsxs("div",{className:"flex items-center justify-center py-2",children:[e.jsx(p,{className:"h-4 w-4 mr-2"}),e.jsx("span",{className:"text-sm",children:"Đang tải vai trò..."})]}):f&&f.length>0?e.jsxs("select",{className:"w-full p-2 border rounded-md",...t.register("roleId"),children:[e.jsx("option",{value:"",children:"-- Chọn vai trò --"}),f.map(s=>e.jsx("option",{value:s.id,children:s.roleName},s.id))]}):e.jsx("p",{className:"text-sm text-muted-foreground",children:"Không có vai trò nào"}),t.formState.errors.roleId&&e.jsx("p",{className:"text-sm text-red-500",children:t.formState.errors.roleId.message})]}),e.jsxs(R,{children:[e.jsx(c,{type:"button",variant:"outline",onClick:()=>y(!1),children:"Thoát"}),e.jsx(c,{type:"submit",disabled:I.isPending,children:I.isPending?e.jsxs(e.Fragment,{children:[e.jsx(p,{className:"mr-2 h-4 w-4"}),"Đang thêm..."]}):"Thêm"})]})]})})]})}),e.jsx(L,{open:q,onOpenChange:E,children:e.jsxs(M,{className:"sm:max-w-[600px]",children:[e.jsx(K,{children:e.jsx(O,{children:"Chi tiết nhân viên"})}),Q?e.jsx("div",{className:"flex justify-center py-8",children:e.jsx(p,{})}):i?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium",children:"Họ tên"}),e.jsx("p",{children:i.fullName||"-"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium",children:"Email"}),e.jsx("p",{children:i.email||"-"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium",children:"Số điện thoại"}),e.jsx("p",{children:i.phone||"-"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium",children:"Loại người dùng"}),e.jsx("p",{children:i.userType||"-"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium",children:"Trạng thái"}),e.jsx(A,{variant:"subtle",colorScheme:i.isActive?"green":"red",children:i.isActive?"Đang hoạt động":"Đã vô hiệu hóa"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium",children:"Đăng nhập cuối"}),e.jsx("p",{children:i.lastLogin?new Date(i.lastLogin).toLocaleString("vi-VN"):"-"})]})]}),i.roleObjects&&i.roleObjects.length>0&&e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium",children:"Vai trò"}),e.jsx("div",{className:"flex flex-wrap gap-2 mt-1",children:i.roleObjects.map((s,n)=>e.jsx(A,{variant:"outline",children:s.roleName},n))})]}),e.jsx(R,{children:e.jsx(c,{onClick:()=>E(!1),children:"Đóng"})})]}):e.jsx("p",{className:"text-center py-4",children:"Không tìm thấy thông tin nhân viên"})]})}),e.jsx(L,{open:P,onOpenChange:S,children:e.jsxs(M,{className:"sm:max-w-[600px]",children:[e.jsx(K,{children:e.jsx(O,{children:"Chỉnh sửa nhân viên"})}),Q?e.jsx("div",{className:"flex justify-center py-8",children:e.jsx(p,{})}):i?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium",children:"Họ tên"}),e.jsx("p",{children:i.fullName||"-"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium",children:"Email"}),e.jsx("p",{children:i.email||"-"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium",children:"Số điện thoại"}),e.jsx("p",{children:i.phone||"-"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium",children:"Loại người dùng"}),e.jsx("p",{children:i.userType||"-"})]})]}),e.jsxs("div",{className:"border-t pt-4",children:[e.jsx("h4",{className:"text-sm font-medium mb-2",children:"Trạng thái tài khoản"}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(c,{variant:i.isActive?"outline":"default",onClick:()=>w.mutate({id:i.id,status:!1}),disabled:!i.isActive||w.isPending,children:"Vô hiệu hóa"}),e.jsx(c,{variant:i.isActive?"default":"outline",onClick:()=>w.mutate({id:i.id,status:!0}),disabled:i.isActive||w.isPending,children:"Kích hoạt"})]})]}),e.jsxs("div",{className:"border-t pt-4",children:[e.jsx("h4",{className:"text-sm font-medium mb-2",children:"Vai trò"}),B?e.jsxs("div",{className:"flex items-center py-2",children:[e.jsx(p,{className:"h-4 w-4 mr-2"}),e.jsx("span",{className:"text-sm",children:"Đang tải vai trò..."})]}):f&&f.length>0?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("select",{className:"w-full p-2 border rounded-md",value:((_=(Z=i.roleObjects)==null?void 0:Z[0])==null?void 0:_.id)||"",onChange:s=>{const n=s.target.value;n&&U.mutate({userId:i.id,roleIds:[n]})},children:[e.jsx("option",{value:"",children:"-- Chọn vai trò --"}),f.map(s=>e.jsx("option",{value:s.id,children:s.roleName},s.id))]}),U.isPending&&e.jsxs("div",{className:"flex items-center text-sm text-muted-foreground",children:[e.jsx(p,{className:"h-3 w-3 mr-2"}),"Đang cập nhật vai trò..."]})]}):e.jsx("p",{className:"text-sm text-muted-foreground",children:"Không có vai trò nào"})]}),e.jsx(R,{children:e.jsx(c,{onClick:()=>S(!1),children:"Đóng"})})]}):e.jsx("p",{className:"text-center py-4",children:"Không tìm thấy thông tin nhân viên"})]})}),e.jsx(Ee,{open:te,onOpenChange:k,children:e.jsxs(Pe,{children:[e.jsxs(ke,{children:[e.jsx(Ie,{children:"Xác nhận xóa nhân viên"}),e.jsx(Ve,{children:"Hành động này sẽ xóa vĩnh viễn nhân viên và không thể khôi phục. Bạn có chắc chắn muốn tiếp tục?"})]}),e.jsxs(Fe,{children:[e.jsx(Ae,{children:"Thoát"}),e.jsx(Le,{onClick:me,className:"bg-red-600 hover:bg-red-700",children:X.isPending?e.jsxs(e.Fragment,{children:[e.jsx(p,{className:"mr-2 h-4 w-4"}),"Đang xóa..."]}):"Xác nhận xóa"})]})]})})]})};export{ms as component};
