import{j as a,O as t,Q as i,S as n,T as o,P as e}from"./index-DFXNGaQ4.js";t({page:o().optional().default(1),pageSize:o().optional().default(e),email:n().optional().default(""),name:n().optional().default(""),phone:n().optional().default(""),sortColumn:n().optional(),sortDescending:i().optional()});t({fullName:n().min(1,"Họ tên không được để trống"),email:n().email("<PERSON>ail không hợp lệ").min(1,"<PERSON>ail không được để trống"),password:n().min(6,"Mật khẩu phải có ít nhất 6 ký tự"),phone:n().min(1,"Số điện thoại không được để trống"),roleId:n().min(1,"<PERSON><PERSON><PERSON> chọn vai trò")});const r=()=>a.jsx("div",{className:"p-10",children:"<PERSON><PERSON> xảy ra lỗi khi tải dữ liệu."});export{r as errorComponent};
