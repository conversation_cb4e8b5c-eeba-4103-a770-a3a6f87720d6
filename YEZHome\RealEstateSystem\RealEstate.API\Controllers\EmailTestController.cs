using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.Interfaces;

namespace RealEstate.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class EmailTestController : ControllerBase
    {
        private readonly IEmailSender _emailSender;
        private readonly ILogger<EmailTestController> _logger;

        public EmailTestController(IEmailSender emailSender, ILogger<EmailTestController> logger)
        {
            _emailSender = emailSender;
            _logger = logger;
        }

        /// <summary>
        /// Test endpoint to send a test email using AWS SES
        /// </summary>
        /// <param name="request">Email test request</param>
        /// <returns>Success or error message</returns>
        [HttpPost("send-test-email")]
        [AllowAnonymous] // Remove this in production
        public async Task<IActionResult> SendTestEmail([FromBody] TestEmailRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                _logger.LogInformation("Sending test email to {Email}", request.ToEmail);

                await _emailSender.SendEmailAsync(
                    request.ToEmail,
                    request.Subject ?? "Test Email from YEZ Home",
                    request.Body ?? "<h1>Test Email</h1><p>This is a test email from YEZ Home system.</p>"
                );

                _logger.LogInformation("Test email sent successfully to {Email}", request.ToEmail);

                return Ok(new { Message = "Test email sent successfully!", ToEmail = request.ToEmail });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send test email to {Email}", request.ToEmail);
                return StatusCode(500, new { Message = "Failed to send email", Error = ex.Message });
            }
        }
    }

    public class TestEmailRequest
    {
        public string ToEmail { get; set; } = string.Empty;
        public string? Subject { get; set; }
        public string? Body { get; set; }
    }
}
