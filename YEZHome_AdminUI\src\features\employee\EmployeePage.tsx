import { useNavigate, useSearch } from '@tanstack/react-router'
import { useState, useMemo, useCallback } from 'react'
import type { UserDto } from '@/lib/types/user'
import { PAGE_SIZE } from '@/lib/enum'

// Import new components
import { 
  AddEmployeeDialog,
  ViewEmployeeDialog,
  EditEmployeeDialog,
  DeleteEmployeeDialog,
  EmployeeTable,
  EmployeeSearchForm
} from './components'

// Import custom hooks
import { useEmployeeData } from './hooks'

export const EmployeePage = () => {
  const navigate = useNavigate()
  const search = useSearch({ from: '/_authenticated/employee/' })

  // State for dialogs
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedEmployee, setSelectedEmployee] = useState<UserDto | null>(null)

  // Convert search params to the format expected by the API
  const searchParams = useMemo(() => ({
    PageNumber: search.page,
    PageSize: search.pageSize,
    Email: search.email || undefined,
    Name: search.name || undefined,
    Phone: search.phone || undefined,
    SortColumn: search.sortColumn,
    SortDescending: search.sortDescending,
  }), [search])

  // Fetch employees based on search params
  const { data: employeesData, isLoading: isLoadingEmployees } = useEmployeeData(searchParams)

  // Memoized search form default values
  const searchFormDefaults = useMemo(() => ({
    email: search.email,
    name: search.name,
    phone: search.phone,
  }), [search.email, search.name, search.phone])

  // Handle search submission
  const handleSearchSubmit = useCallback((data: { email?: string; name?: string; phone?: string }) => {
    navigate({
      to: '/employee',
      search: {
        ...search,
        ...data,
        page: 1, // Reset to first page on new search
      }
    })
  }, [navigate, search])

  // Handle pagination
  const handlePageChange = useCallback((page: number, pageSize: number) => {
    navigate({
      to: '/employee',
      search: {
        ...search,
        page,
        pageSize,
      }
    })
  }, [navigate, search])

  // Row action handlers
  const handleViewDetails = useCallback((employee: UserDto) => {
    setSelectedEmployee(employee)
    setIsViewDialogOpen(true)
  }, [])

  const handleEdit = useCallback((employee: UserDto) => {
    setSelectedEmployee(employee)
    setIsEditDialogOpen(true)
  }, [])

  const handleDelete = useCallback((employee: UserDto) => {
    setSelectedEmployee(employee)
    setIsDeleteDialogOpen(true)
  }, [])

  // Dialog close handlers
  const handleCloseViewDialog = useCallback(() => {
    setIsViewDialogOpen(false)
    setSelectedEmployee(null)
  }, [])

  const handleCloseEditDialog = useCallback(() => {
    setIsEditDialogOpen(false)
    setSelectedEmployee(null)
  }, [])

  const handleCloseDeleteDialog = useCallback(() => {
    setIsDeleteDialogOpen(false)
    setSelectedEmployee(null)
  }, [])

  // Memoized table props
  const tableProps = useMemo(() => ({
    data: employeesData?.items || [],
    isLoading: isLoadingEmployees,
    totalCount: employeesData?.totalCount || 0,
    pageCount: employeesData?.pageCount || 1,
    currentPage: searchParams.PageNumber || 1,
    pageSize: searchParams.PageSize || PAGE_SIZE,
    onPageChange: handlePageChange,
    onView: handleViewDetails,
    onEdit: handleEdit,
    onDelete: handleDelete,
  }), [
    employeesData,
    isLoadingEmployees,
    searchParams.PageNumber,
    searchParams.PageSize,
    handlePageChange,
    handleViewDetails,
    handleEdit,
    handleDelete,
  ])

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Quản lý nhân viên</h1>
        <AddEmployeeDialog />
      </div>

      {/* Search form */}
      <EmployeeSearchForm
        defaultValues={searchFormDefaults}
        onSubmit={handleSearchSubmit}
      />

      {/* Table */}
      <EmployeeTable {...tableProps} />

      {/* Dialogs */}
      <ViewEmployeeDialog
        employee={selectedEmployee}
        isOpen={isViewDialogOpen}
        onClose={handleCloseViewDialog}
      />

      <EditEmployeeDialog
        employee={selectedEmployee}
        isOpen={isEditDialogOpen}
        onClose={handleCloseEditDialog}
      />

      <DeleteEmployeeDialog
        employee={selectedEmployee}
        isOpen={isDeleteDialogOpen}
        onClose={handleCloseDeleteDialog}
      />
    </div>
  )
}
