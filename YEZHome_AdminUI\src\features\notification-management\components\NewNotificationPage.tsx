import { useNavigate } from '@tanstack/react-router';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { NotificationForm } from './notification-form';

export const NewNotificationPage = () => {
  const navigate = useNavigate();

  const handleCancel = () => {
    history.go(-1);
  };

  const handleSuccess = () => {
    navigate({
      to: '/notification',
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="icon" onClick={() => {
          history.go(-1);
        }}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold">Tạo thông báo mới</h1>
          <p className="text-gray-600 mt-1">
            T<PERSON><PERSON> thông báo mới để gửi đến người dùng
          </p>
        </div>
      </div>

      <NotificationForm
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  );
}; 