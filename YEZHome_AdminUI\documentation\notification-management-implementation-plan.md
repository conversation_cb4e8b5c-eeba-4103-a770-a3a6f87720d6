# Notification Management Implementation Plan

## Overview
This document outlines the implementation plan for the Notification Management feature in the YEZHome Admin UI. The feature allows administrators to create and manage notifications for users.

## Requirements
The UI should follow same template applied for `property`

### Functional Requirements
1. Administrators can create new notifications for users
2. Notifications can be of three types: SYSTEM, PROMOTION, and NEWS
3. Administrators can link notifications to:
   - Custom URLs (by direct input)
4. Administrators can view a list of all notifications
5. Administrators can filter and search notifications

### API Integration
- POST `/api/Notification` - Create a new notification
  ```json
  {
    "userId": "fe8726b2-8a46-cfcc-7beb-d734b5f1cf29",
    "type": "SYSTEM",
    "title": "Thông báo hệ thống mới",
    "message": "Nội dung thông báo",
    "relatedEntityId": "urn:uuid:6b764a10-eb85-2494-ca46-e83447b06aa0",
    "relatedPropertyId": "bf61b7e8-840d-e8ec-e10e-83c91039a5f9",
    "actionUrl": "https://example.com"
  }
  ```
- GET `/api/Notification` - Retrieve list of notifications
- GET `/api/Notification/{id}` - Get notification details
- DELETE `/api/Notification/{id}` - Delete a notification

## Implementation Plan

### 1. Data Models and Types

#### Notification Type
Create interfaces and types in `src/lib/types/notification.ts`:
```typescript
export interface Notification {
  id: string;
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  relatedEntityId?: string;
  relatedPropertyId?: string;
  actionUrl?: string;
  isRead: boolean;
  createdAt: string;
  updatedAt: string;
}

export type NotificationType = 'SYSTEM' | 'PROMOTION' | 'NEWS';

export interface NotificationCreateRequest {
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  relatedEntityId?: string;
  relatedPropertyId?: string;
  actionUrl?: string;
}

export interface NotificationFilter {
  page: number;
  pageSize: number;
  type?: NotificationType;
  startDate?: string;
  endDate?: string;
}
```

### 2. API Services

Create a notification service in `src/services/notification-service.ts`:
```typescript
import { axiosInstance } from './axios-config';
import { Notification, NotificationCreateRequest, NotificationFilter } from '@/lib/types/notification';

export const notificationService = {
  getNotifications: async (filter: NotificationFilter) => {
    // Prepare date parameters in the correct format if they exist
    const params = {
      ...filter,
      startDate: filter.startDate ? new Date(filter.startDate).toISOString() : undefined,
      endDate: filter.endDate ? new Date(filter.endDate).toISOString() : undefined
    };
    
    const response = await axiosInstance.get('/api/Notification', { params });
    return response.data;
  },
  
  getNotificationById: async (id: string) => {
    const response = await axiosInstance.get(`/api/Notification/${id}`);
    return response.data;
  },
  
  createNotification: async (notification: NotificationCreateRequest) => {
    const response = await axiosInstance.post('/api/Notification', notification);
    return response.data;
  },
  
  deleteNotification: async (id: string) => {
    const response = await axiosInstance.delete(`/api/Notification/${id}`);
    return response.data;
  }
};
```

### 3. UI Components

#### Notification Form Component
Create a form component in `src/features/notification-management/components/notification-form.tsx`:
- Form for creating new notifications
- Dropdown for selecting notification type (SYSTEM, PROMOTION, NEWS)
- Input fields for title 
- Rich text editor for message
- Input field for custom URL (validate this field should be a correct url format)
- Submit button

#### Notifications Table Component
Create a table component in `src/features/notification-management/components/notifications-table.tsx`:
- Display list of notifications with columns:
  - Type
  - Title (truncated)
  - Message (truncated)
  - Created Date
  - Actions (View, Delete)
- Pagination
- Filtering by type, date range

#### Filter Components

##### Type Filter Component
Create a component in `src/features/notification-management/components/filters/type-filter.tsx`:
- Dropdown or segmented control for notification types
- Options: All, SYSTEM, PROMOTION, NEWS
- Support for multiple selection
- Visual indication of active filters

##### Date Range Filter Component
Create a component in `src/features/notification-management/components/filters/date-range-filter.tsx`:
- Two date picker inputs (From date and To date)
- Calendar popup for date selection
- Validation to ensure From date is before To date
- Preset date range options (Today, Last 7 days, Last 30 days, etc.)
- Clear button to reset date selection

### 4. Pages

#### Notifications List Page
Create a page in `src/features/notification-management/components/NotificationPage.tsx`:
- Heading and description
- Filter controls
- Notifications table
- Create notification button

##### Advanced Filtering Functionality
The notification list page will include a comprehensive filtering system:

1. **Filter by Notification Type**
   - Dropdown menu allowing selection of notification types:
     - All types (default)
     - SYSTEM
     - PROMOTION
     - NEWS
   - Multiple selection should be supported
   - Visual indicators showing active filters

2. **Filter by Date Range**
   - Date range picker with two fields:
     - From date: Selects notifications created on or after this date
     - To date: Selects notifications created on or before this date
   - Calendar UI for easy date selection
   - Preset options for common date ranges:
     - Today
     - Last 7 days
     - Last 30 days
     - Last 90 days
     - Custom range

3. **Filter Persistence**
   - Filter selections should be preserved when navigating away and returning
   - URL parameters should reflect current filter state for bookmarking
   - Clear filters button to reset all filters to default values

4. **Filter UI Components**
   - Filter section should be collapsible to save space when not in use
   - Mobile-responsive design for all filter controls
   - Visual feedback when filters are applied

#### Create Notification Page
Create a page in `src/features/notification-management/components/NewNotificationPage.tsx`:
- Heading and description
- Notification form
- Cancel and Submit buttons

#### Notification Detail Page
Create a page in `src/features/notification-management/components/NotificationDetailPage.tsx`:
- Display all notification details
- Delete button
- Back button

### 5. Routes

Add new routes in `src/routes/_authenticated/notification/`:
- `index.tsx` - List all notifications

### 6. UI/UX Considerations

- Consistent styling with the rest of the application using Shadcn UI and Tailwind
- Responsive design for all screen sizes
- Form validation for required fields
- Confirmation dialogs for delete actions
- Success/error notifications for all actions
- If you want to install the library stop and confirm with me first (give me at least 2 libraries for me to choose first)

### 7. Testing

- Unit tests for components
- Integration tests for API services
- End-to-end tests for notification creation flow

### 8. Filter State Management

To effectively manage the filter state across the application:

1. **Filter State Hook**
   Create a custom hook in `src/features/notification-management/hooks/use-notification-filters.ts`:
   ```typescript
   export const useNotificationFilters = () => {
     const [filters, setFilters] = useState<NotificationFilter>({
       page: 1,
       pageSize: 50,
       type: undefined,
       startDate: undefined,
       endDate: undefined
     });
     
     // Functions to update individual filters
     const updateType = (type: NotificationType | undefined) => {
       setFilters(prev => ({ ...prev, type, page: 1 }));
     };
     
     const updateDateRange = (startDate: string | undefined, endDate: string | undefined) => {
       setFilters(prev => ({ ...prev, startDate, endDate, page: 1 }));
     };     
     
     const updatePage = (page: number) => {
       setFilters(prev => ({ ...prev, page }));
     };
     
     const resetFilters = () => {
       setFilters({
         page: 1,
         pageSize: 50,
         type: undefined,
         startDate: undefined,
         endDate: undefined
       });
     };
     
     return {
       filters,
       updateType,
       updateDateRange,
       updatePage,
       resetFilters
     };
   };
   ```

2. **URL Synchronization**
   Implement URL synchronization to preserve filter state:
   ```typescript
   // In the NotificationsPage component
   const { filters, updateType, updateDateRange, updatePage, resetFilters } = useNotificationFilters();
   const navigate = useNavigate();
   
   // Sync filters to URL
   useEffect(() => {
     const params = new URLSearchParams();
     if (filters.type) params.append('type', filters.type);    
     if (filters.startDate) params.append('from', filters.startDate);
     if (filters.endDate) params.append('to', filters.endDate);
     if (filters.page > 1) params.append('page', filters.page.toString());
     
     navigate({ search: params.toString() }, { replace: true });
   }, [filters, navigate]);
   
   // Initialize filters from URL on component mount
   useEffect(() => {
     const params = new URLSearchParams(location.search);
     const typeParam = params.get('type') as NotificationType | null;
     const fromParam = params.get('from');
     const toParam = params.get('to');
     const pageParam = params.get('page');
     
     if (typeParam || fromParam || toParam || pageParam) {
       setFilters({
         page: pageParam ? parseInt(pageParam, 10) : 1,
         pageSize: 50,
         type: typeParam || undefined,
         startDate: fromParam || undefined,
         endDate: toParam || undefined
       });
     }
   }, [location.search]);
   ```

## Conclusion

This implementation plan provides a comprehensive approach to building the Notification Management feature for the YEZHome Admin UI. The feature will allow administrators to create and manage notifications of different types and link them to various entities within the system. The advanced filtering capabilities will enable administrators to efficiently manage and find notifications based on type, date range.