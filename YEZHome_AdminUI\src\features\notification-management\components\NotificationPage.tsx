import { useState } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus, RefreshCw } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { NotificationsTable } from './notifications-table';
import { TypeFilter } from './filters/type-filter';
import { DateRangeFilter } from './filters/date-range-filter';
import { useNotificationFilters } from '../hooks/use-notification-filters';
import notificationService from '@/services/notification-service';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

export const NotificationPage = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [deleteId, setDeleteId] = useState<string | null>(null);
  
  const {
    filters,
    updateType,
    updateDateRange,
    updatePage,
    resetFilters
  } = useNotificationFilters();

  // Delete notification mutation
  const deleteNotificationMutation = useMutation({
    mutationFn: (id: string) => notificationService.deleteNotification(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      toast({
        title: 'Thành công',
        description: 'Thông báo đã được xóa thành công',
      });
      setDeleteId(null);
    },
    onError: (error: any) => {
      toast({
        title: 'Lỗi',
        description: error.response?.data?.message || 'Có lỗi xảy ra khi xóa thông báo',
        variant: 'destructive',
      });
      setDeleteId(null);
    },
  });

  const handleCreateNotification = () => {
    navigate({
      to: '/notification/new',
    });
  };

  const handleViewDetails = (id: string) => {
    navigate({
      to: '/notification/$notificationId',
      params: { notificationId: id },
    });
  };

  const handleDelete = (id: string) => {
    setDeleteId(id);
  };

  const handleConfirmDelete = () => {
    if (deleteId) {
      deleteNotificationMutation.mutate(deleteId);
    }
  };

  const handleResetFilters = () => {
    resetFilters();
  };

  const hasActiveFilters = filters.type || filters.startDate || filters.endDate;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Quản lý thông báo</h1>
          <p className="text-gray-600 mt-1">
            Quản lý và tạo thông báo cho người dùng
          </p>
        </div>

        <Button onClick={handleCreateNotification} className="gap-2">
          <Plus className="h-4 w-4" />
          Tạo thông báo
        </Button>
      </div>

      {/* Filter Section */}
      <Card>
        <CardContent>
          <div className="flex flex-wrap gap-3 items-center">
            <TypeFilter
              selectedType={filters.type}
              onTypeChange={updateType}
            />
            
            <DateRangeFilter
              startDate={filters.startDate}
              endDate={filters.endDate}
              onDateRangeChange={updateDateRange}
            />

            {hasActiveFilters && (
              <Button
                variant="outline"
                onClick={handleResetFilters}
                className="gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Xóa bộ lọc
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Table Section */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách thông báo</CardTitle>
        </CardHeader>
        <CardContent>
          <NotificationsTable
            filters={filters}
            onPageChange={updatePage}
            onViewDetails={handleViewDetails}
            onDelete={handleDelete}
          />
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deleteId} onOpenChange={() => setDeleteId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận xóa</AlertDialogTitle>
            <AlertDialogDescription>
              Bạn có chắc chắn muốn xóa thông báo này không? Hành động này không thể hoàn tác.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Hủy</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              Xóa
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}; 