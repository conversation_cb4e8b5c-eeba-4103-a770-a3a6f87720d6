import{j as t,ab as l,ac as d,N as o,ad as i,X as r,ae as c,af as g,ag as u}from"./index-DFXNGaQ4.js";function p({...a}){return t.jsx(l,{"data-slot":"dialog",...a})}function f({...a}){return t.jsx(g,{"data-slot":"dialog-portal",...a})}function x({className:a,...e}){return t.jsx(u,{"data-slot":"dialog-overlay",className:o("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...e})}function j({className:a,children:e,showCloseButton:s=!0,...n}){return t.jsxs(f,{"data-slot":"dialog-portal",children:[t.jsx(x,{}),t.jsxs(d,{"data-slot":"dialog-content",className:o("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...n,children:[e,s&&t.jsxs(i,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[t.jsx(r,{}),t.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function b({className:a,...e}){return t.jsx("div",{"data-slot":"dialog-header",className:o("flex flex-col gap-2 text-center sm:text-left",a),...e})}function v({className:a,...e}){return t.jsx("div",{"data-slot":"dialog-footer",className:o("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...e})}function h({className:a,...e}){return t.jsx(c,{"data-slot":"dialog-title",className:o("text-lg leading-none font-semibold",a),...e})}export{p as D,j as a,b,h as c,v as d};
