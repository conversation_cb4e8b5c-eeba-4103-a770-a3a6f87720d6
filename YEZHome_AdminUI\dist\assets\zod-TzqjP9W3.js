import{g as h,s as l,h as E}from"./form-lBragMB2.js";import{ax as j,ay as V,az as b}from"./index-DFXNGaQ4.js";const v=(s,o,r)=>{if(s&&"reportValidity"in s){const e=h(r,o);s.setCustomValidity(e&&e.message||""),s.reportValidity()}},m=(s,o)=>{for(const r in o.fields){const e=o.fields[r];e&&e.ref&&"reportValidity"in e.ref?v(e.ref,r,s):e&&e.refs&&e.refs.forEach(t=>v(t,r,s))}},p=(s,o)=>{o.shouldUseNativeValidation&&m(s,o);const r={};for(const e in s){const t=h(o.fields,e),i=Object.assign(s[e]||{},{ref:t&&t.ref});if(_(o.names||Object.keys(s),e)){const n=Object.assign({},h(r,e));l(n,"root",i),l(r,e,n)}else l(r,e,i)}return r},_=(s,o)=>{const r=y(o);return s.some(e=>y(e).match(`^${r}\\.\\d+`))};function y(s){return s.replace(/\]|\[/g,"")}function g(s,o){try{var r=s()}catch(e){return o(e)}return r&&r.then?r.then(void 0,o):r}function w(s,o){for(var r={};s.length;){var e=s[0],t=e.code,i=e.message,n=e.path.join(".");if(!r[n])if("unionErrors"in e){var a=e.unionErrors[0].errors[0];r[n]={message:a.message,type:a.code}}else r[n]={message:i,type:t};if("unionErrors"in e&&e.unionErrors.forEach(function(f){return f.errors.forEach(function(d){return s.push(d)})}),o){var c=r[n].types,u=c&&c[e.code];r[n]=E(n,o,r,t,u?[].concat(u,e.message):e.message)}s.shift()}return r}function N(s,o){for(var r={};s.length;){var e=s[0],t=e.code,i=e.message,n=e.path.join(".");if(!r[n])if(e.code==="invalid_union"){var a=e.errors[0][0];r[n]={message:a.message,type:a.code}}else r[n]={message:i,type:t};if(e.code==="invalid_union"&&e.errors.forEach(function(f){return f.forEach(function(d){return s.push(d)})}),o){var c=r[n].types,u=c&&c[e.code];r[n]=E(n,o,r,t,u?[].concat(u,e.message):e.message)}s.shift()}return r}function U(s,o,r){if(r===void 0&&(r={}),function(e){return"_def"in e&&typeof e._def=="object"&&"typeName"in e._def}(s))return function(e,t,i){try{return Promise.resolve(g(function(){return Promise.resolve(s[r.mode==="sync"?"parse":"parseAsync"](e,o)).then(function(n){return i.shouldUseNativeValidation&&m({},i),{errors:{},values:r.raw?Object.assign({},e):n}})},function(n){if(function(a){return Array.isArray(a==null?void 0:a.issues)}(n))return{values:{},errors:p(w(n.errors,!i.shouldUseNativeValidation&&i.criteriaMode==="all"),i)};throw n}))}catch(n){return Promise.reject(n)}};if(function(e){return"_zod"in e&&typeof e._zod=="object"}(s))return function(e,t,i){try{return Promise.resolve(g(function(){return Promise.resolve((r.mode==="sync"?j:V)(s,e,o)).then(function(n){return i.shouldUseNativeValidation&&m({},i),{errors:{},values:r.raw?Object.assign({},e):n}})},function(n){if(function(a){return a instanceof b}(n))return{values:{},errors:p(N(n.issues,!i.shouldUseNativeValidation&&i.criteriaMode==="all"),i)};throw n}))}catch(n){return Promise.reject(n)}};throw new Error("Invalid input: not a Zod schema")}export{U as a};
