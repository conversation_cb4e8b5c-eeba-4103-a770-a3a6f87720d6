import{av as p,a as x,l as b,a7 as v,b as j,m as f,Y as r,j as t,L as y,B as l}from"./index-DFXNGaQ4.js";import{b as c}from"./blog-service-Dpp8ABmX.js";import{B as C}from"./blog-post-form-BWQUA0yd.js";import{C as N,a as B,b as P,d as A}from"./card-Dikj3j-6.js";import{A as u}from"./arrow-left-CbfRM3Sk.js";import"./form-lBragMB2.js";import"./label-BscdIpbp.js";import"./zod-TzqjP9W3.js";import"./input-D3ozjVi3.js";import"./index-BmxYfgzo.js";import"./select-DIbexXwn.js";import"./index-BpksVOb4.js";import"./chevron-down-DuS99v9n.js";function L(){const{blogId:i}=p.useParams(),a=x(),n=b(),{history:d}=v(),{data:e,isLoading:m}=j({queryKey:["blog",i],queryFn:()=>c.getBlogPost(i)}),o=f({mutationFn:s=>c.updateBlogPost(i,{...s,authorID:(e==null?void 0:e.authorID)||"",publishedAt:s.publishedAt||null}),onSuccess:()=>{n.invalidateQueries({queryKey:["blog",i]}),n.invalidateQueries({queryKey:["blogPosts"]}),r({title:"Thành công",description:"Bài viết đã được cập nhật thành công.",variant:"success"}),a({to:`/blog/${i}`})},onError:s=>{r({title:"Lỗi",description:"Không thể cập nhật bài viết. Vui lòng thử lại sau.",variant:"destructive"}),console.error("Error updating blog post:",s)}}),h=s=>{o.mutate(s)};if(m)return t.jsx(y,{});if(!e)return t.jsxs("div",{className:"text-center py-10",children:[t.jsx("h2",{className:"text-2xl font-bold text-red-600",children:"Không tìm thấy bài viết"}),t.jsx("p",{className:"text-gray-600",children:"Bài viết bạn đang tìm không tồn tại hoặc đã bị xóa."}),t.jsxs(l,{variant:"outline",className:"mt-4",onClick:()=>a({to:"/blog"}),children:[t.jsx(u,{className:"mr-2 h-4 w-4"})," Quay lại"]})]});const g={title:e.title,content:e.content,featuredImage:e.featuredImage||"",tags:e.tags||"",status:e.status,publishedAt:e.publishedAt||null};return t.jsxs("div",{className:"space-y-6",children:[t.jsx("div",{className:"flex items-center justify-between",children:t.jsxs("div",{className:"flex items-center gap-4",children:[t.jsx(l,{variant:"outline",size:"icon",onClick:()=>{d.go(-1)},children:t.jsx(u,{className:"h-4 w-4"})}),t.jsx("h1",{className:"text-2xl font-bold",children:"Chỉnh sửa bài viết"})]})}),t.jsxs(N,{children:[t.jsx(B,{children:t.jsx(P,{className:"text-xl",children:"Thông tin bài viết"})}),t.jsx(A,{children:t.jsx(C,{defaultValues:g,onSubmit:h,isSubmitting:o.isPending})})]})]})}const V=L;export{V as component};
