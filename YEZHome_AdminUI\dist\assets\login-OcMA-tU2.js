import{u as f,r as N,j as e,B as y}from"./index-DFXNGaQ4.js";import{C,a as b,b as v,c as w,d as E,e as F}from"./card-Dikj3j-6.js";import{I as m}from"./input-D3ozjVi3.js";import{L as d}from"./label-BscdIpbp.js";const D=function(){var i,c;const{login:o,isLoading:n,isError:h,error:s}=f(),[a,x]=N.useState({email:"",password:""}),r=t=>{const{name:u,value:j}=t.target;x(g=>({...g,[u]:j}))},p=async t=>{t.preventDefault(),o(a)},l=h?((c=(i=s==null?void 0:s.response)==null?void 0:i.data)==null?void 0:c.message)||"Đ<PERSON>ng nhập thất bại. Vui lòng thử lại.":null;return e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:e.jsxs(C,{className:"w-full max-w-md",children:[e.jsxs(b,{className:"space-y-1",children:[e.jsx(v,{className:"text-2xl font-bold text-center",children:"YEZ Home Admin"}),e.jsx(w,{className:"text-center",children:"Đăng nhập để truy cập vào hệ thống quản trị"})]}),e.jsx(E,{children:e.jsxs("form",{onSubmit:p,className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(d,{htmlFor:"email",children:"Email"}),e.jsx(m,{id:"email",name:"email",placeholder:"Nhập email",required:!0,value:a.email,onChange:r})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(d,{htmlFor:"password",children:"Mật khẩu"}),e.jsx("a",{href:"#",className:"text-sm text-blue-500 hover:text-blue-700",children:"Quên mật khẩu?"})]}),e.jsx(m,{id:"password",name:"password",type:"password",placeholder:"Nhập mật khẩu",required:!0,value:a.password,onChange:r})]}),l&&e.jsx("div",{className:"text-red-500 text-sm",children:l}),e.jsx(y,{type:"submit",className:"w-full",disabled:n,children:n?"Đang đăng nhập...":"Đăng nhập"})]})}),e.jsx(F,{className:"flex justify-center",children:e.jsxs("p",{className:"text-sm text-gray-500",children:["© ",new Date().getFullYear()," YEZ Home Admin"]})})]})})};export{D as component};
