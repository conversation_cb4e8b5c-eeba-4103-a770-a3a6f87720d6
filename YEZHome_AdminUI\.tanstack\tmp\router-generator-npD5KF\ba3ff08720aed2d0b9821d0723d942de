/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as LoginRouteImport } from './routes/login'
import { Route as AuthenticatedRouteImport } from './routes/_authenticated'
import { Route as IndexRouteImport } from './routes/index'
import { Route as AuthenticatedDashboardRouteImport } from './routes/_authenticated/dashboard'
import { Route as AuthenticatedPropertyIndexRouteImport } from './routes/_authenticated/property/index'
import { Route as AuthenticatedEmployeeIndexRouteImport } from './routes/_authenticated/employee/index'
import { Route as AuthenticatedCustomerIndexRouteImport } from './routes/_authenticated/customer/index'
import { Route as AuthenticatedBlogIndexRouteImport } from './routes/_authenticated/blog/index'
import { Route as AuthenticatedPropertyPropertyIdRouteImport } from './routes/_authenticated/property/$propertyId'
import { Route as AuthenticatedCustomerCustomerIdRouteImport } from './routes/_authenticated/customer/$customerId'
import { Route as AuthenticatedBlogNewRouteImport } from './routes/_authenticated/blog/new'
import { Route as AuthenticatedBlogBlogIdIndexRouteImport } from './routes/_authenticated/blog/$blogId/index'
import { Route as AuthenticatedBlogBlogIdEditRouteImport } from './routes/_authenticated/blog/$blogId/edit'

const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedRoute = AuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedDashboardRoute = AuthenticatedDashboardRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedPropertyIndexRoute =
  AuthenticatedPropertyIndexRouteImport.update({
    id: '/property/',
    path: '/property/',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedEmployeeIndexRoute =
  AuthenticatedEmployeeIndexRouteImport.update({
    id: '/employee/',
    path: '/employee/',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedCustomerIndexRoute =
  AuthenticatedCustomerIndexRouteImport.update({
    id: '/customer/',
    path: '/customer/',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedBlogIndexRoute = AuthenticatedBlogIndexRouteImport.update({
  id: '/blog/',
  path: '/blog/',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedPropertyPropertyIdRoute =
  AuthenticatedPropertyPropertyIdRouteImport.update({
    id: '/property/$propertyId',
    path: '/property/$propertyId',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedCustomerCustomerIdRoute =
  AuthenticatedCustomerCustomerIdRouteImport.update({
    id: '/customer/$customerId',
    path: '/customer/$customerId',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedBlogNewRoute = AuthenticatedBlogNewRouteImport.update({
  id: '/blog/new',
  path: '/blog/new',
  getParentRoute: () => AuthenticatedRoute,
} as any)
const AuthenticatedBlogBlogIdIndexRoute =
  AuthenticatedBlogBlogIdIndexRouteImport.update({
    id: '/blog/$blogId/',
    path: '/blog/$blogId/',
    getParentRoute: () => AuthenticatedRoute,
  } as any)
const AuthenticatedBlogBlogIdEditRoute =
  AuthenticatedBlogBlogIdEditRouteImport.update({
    id: '/blog/$blogId/edit',
    path: '/blog/$blogId/edit',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/login': typeof LoginRoute
  '/dashboard': typeof AuthenticatedDashboardRoute
  '/blog/new': typeof AuthenticatedBlogNewRoute
  '/customer/$customerId': typeof AuthenticatedCustomerCustomerIdRoute
  '/property/$propertyId': typeof AuthenticatedPropertyPropertyIdRoute
  '/blog': typeof AuthenticatedBlogIndexRoute
  '/customer': typeof AuthenticatedCustomerIndexRoute
  '/employee': typeof AuthenticatedEmployeeIndexRoute
  '/property': typeof AuthenticatedPropertyIndexRoute
  '/blog/$blogId/edit': typeof AuthenticatedBlogBlogIdEditRoute
  '/blog/$blogId': typeof AuthenticatedBlogBlogIdIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/login': typeof LoginRoute
  '/dashboard': typeof AuthenticatedDashboardRoute
  '/blog/new': typeof AuthenticatedBlogNewRoute
  '/customer/$customerId': typeof AuthenticatedCustomerCustomerIdRoute
  '/property/$propertyId': typeof AuthenticatedPropertyPropertyIdRoute
  '/blog': typeof AuthenticatedBlogIndexRoute
  '/customer': typeof AuthenticatedCustomerIndexRoute
  '/employee': typeof AuthenticatedEmployeeIndexRoute
  '/property': typeof AuthenticatedPropertyIndexRoute
  '/blog/$blogId/edit': typeof AuthenticatedBlogBlogIdEditRoute
  '/blog/$blogId': typeof AuthenticatedBlogBlogIdIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/_authenticated': typeof AuthenticatedRouteWithChildren
  '/login': typeof LoginRoute
  '/_authenticated/dashboard': typeof AuthenticatedDashboardRoute
  '/_authenticated/blog/new': typeof AuthenticatedBlogNewRoute
  '/_authenticated/customer/$customerId': typeof AuthenticatedCustomerCustomerIdRoute
  '/_authenticated/property/$propertyId': typeof AuthenticatedPropertyPropertyIdRoute
  '/_authenticated/blog/': typeof AuthenticatedBlogIndexRoute
  '/_authenticated/customer/': typeof AuthenticatedCustomerIndexRoute
  '/_authenticated/employee/': typeof AuthenticatedEmployeeIndexRoute
  '/_authenticated/property/': typeof AuthenticatedPropertyIndexRoute
  '/_authenticated/blog/$blogId/edit': typeof AuthenticatedBlogBlogIdEditRoute
  '/_authenticated/blog/$blogId/': typeof AuthenticatedBlogBlogIdIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/login'
    | '/dashboard'
    | '/blog/new'
    | '/customer/$customerId'
    | '/property/$propertyId'
    | '/blog'
    | '/customer'
    | '/employee'
    | '/property'
    | '/blog/$blogId/edit'
    | '/blog/$blogId'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/login'
    | '/dashboard'
    | '/blog/new'
    | '/customer/$customerId'
    | '/property/$propertyId'
    | '/blog'
    | '/customer'
    | '/employee'
    | '/property'
    | '/blog/$blogId/edit'
    | '/blog/$blogId'
  id:
    | '__root__'
    | '/'
    | '/_authenticated'
    | '/login'
    | '/_authenticated/dashboard'
    | '/_authenticated/blog/new'
    | '/_authenticated/customer/$customerId'
    | '/_authenticated/property/$propertyId'
    | '/_authenticated/blog/'
    | '/_authenticated/customer/'
    | '/_authenticated/employee/'
    | '/_authenticated/property/'
    | '/_authenticated/blog/$blogId/edit'
    | '/_authenticated/blog/$blogId/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AuthenticatedRoute: typeof AuthenticatedRouteWithChildren
  LoginRoute: typeof LoginRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated/dashboard': {
      id: '/_authenticated/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof AuthenticatedDashboardRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/property/': {
      id: '/_authenticated/property/'
      path: '/property'
      fullPath: '/property'
      preLoaderRoute: typeof AuthenticatedPropertyIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/employee/': {
      id: '/_authenticated/employee/'
      path: '/employee'
      fullPath: '/employee'
      preLoaderRoute: typeof AuthenticatedEmployeeIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/customer/': {
      id: '/_authenticated/customer/'
      path: '/customer'
      fullPath: '/customer'
      preLoaderRoute: typeof AuthenticatedCustomerIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/blog/': {
      id: '/_authenticated/blog/'
      path: '/blog'
      fullPath: '/blog'
      preLoaderRoute: typeof AuthenticatedBlogIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/property/$propertyId': {
      id: '/_authenticated/property/$propertyId'
      path: '/property/$propertyId'
      fullPath: '/property/$propertyId'
      preLoaderRoute: typeof AuthenticatedPropertyPropertyIdRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/customer/$customerId': {
      id: '/_authenticated/customer/$customerId'
      path: '/customer/$customerId'
      fullPath: '/customer/$customerId'
      preLoaderRoute: typeof AuthenticatedCustomerCustomerIdRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/blog/new': {
      id: '/_authenticated/blog/new'
      path: '/blog/new'
      fullPath: '/blog/new'
      preLoaderRoute: typeof AuthenticatedBlogNewRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/blog/$blogId/': {
      id: '/_authenticated/blog/$blogId/'
      path: '/blog/$blogId'
      fullPath: '/blog/$blogId'
      preLoaderRoute: typeof AuthenticatedBlogBlogIdIndexRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/blog/$blogId/edit': {
      id: '/_authenticated/blog/$blogId/edit'
      path: '/blog/$blogId/edit'
      fullPath: '/blog/$blogId/edit'
      preLoaderRoute: typeof AuthenticatedBlogBlogIdEditRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
  }
}

interface AuthenticatedRouteChildren {
  AuthenticatedDashboardRoute: typeof AuthenticatedDashboardRoute
  AuthenticatedBlogNewRoute: typeof AuthenticatedBlogNewRoute
  AuthenticatedCustomerCustomerIdRoute: typeof AuthenticatedCustomerCustomerIdRoute
  AuthenticatedPropertyPropertyIdRoute: typeof AuthenticatedPropertyPropertyIdRoute
  AuthenticatedBlogIndexRoute: typeof AuthenticatedBlogIndexRoute
  AuthenticatedCustomerIndexRoute: typeof AuthenticatedCustomerIndexRoute
  AuthenticatedEmployeeIndexRoute: typeof AuthenticatedEmployeeIndexRoute
  AuthenticatedPropertyIndexRoute: typeof AuthenticatedPropertyIndexRoute
  AuthenticatedBlogBlogIdEditRoute: typeof AuthenticatedBlogBlogIdEditRoute
  AuthenticatedBlogBlogIdIndexRoute: typeof AuthenticatedBlogBlogIdIndexRoute
}

const AuthenticatedRouteChildren: AuthenticatedRouteChildren = {
  AuthenticatedDashboardRoute: AuthenticatedDashboardRoute,
  AuthenticatedBlogNewRoute: AuthenticatedBlogNewRoute,
  AuthenticatedCustomerCustomerIdRoute: AuthenticatedCustomerCustomerIdRoute,
  AuthenticatedPropertyPropertyIdRoute: AuthenticatedPropertyPropertyIdRoute,
  AuthenticatedBlogIndexRoute: AuthenticatedBlogIndexRoute,
  AuthenticatedCustomerIndexRoute: AuthenticatedCustomerIndexRoute,
  AuthenticatedEmployeeIndexRoute: AuthenticatedEmployeeIndexRoute,
  AuthenticatedPropertyIndexRoute: AuthenticatedPropertyIndexRoute,
  AuthenticatedBlogBlogIdEditRoute: AuthenticatedBlogBlogIdEditRoute,
  AuthenticatedBlogBlogIdIndexRoute: AuthenticatedBlogBlogIdIndexRoute,
}

const AuthenticatedRouteWithChildren = AuthenticatedRoute._addFileChildren(
  AuthenticatedRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AuthenticatedRoute: AuthenticatedRouteWithChildren,
  LoginRoute: LoginRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
