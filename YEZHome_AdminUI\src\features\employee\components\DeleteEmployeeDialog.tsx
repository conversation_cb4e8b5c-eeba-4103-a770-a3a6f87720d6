import { useMutation, useQueryClient } from '@tanstack/react-query'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog'
import { Spinner } from '@/components/ui/spinner'
import { toast } from '@/components/ui/use-toast'
import userService from '@/services/user-service'
import type { UserDto } from '@/lib/types/user'

interface DeleteEmployeeDialogProps {
  employee: UserDto | null
  isOpen: boolean
  onClose: () => void
}

export const DeleteEmployeeDialog = ({ employee, isOpen, onClose }: DeleteEmployeeDialogProps) => {
  const queryClient = useQueryClient()

  const deleteEmployeeMutation = useMutation({
    mutationFn: (id: string) => userService.deleteUser(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] })
      onClose()
      toast({
        title: "Thành công",
        description: "Đã xóa nhân viên",
        variant: "success",
      })
    },
    onError: (error) => {
      toast({
        title: "Lỗi",
        description: "Không thể xóa nhân viên. Vui lòng thử lại.",
        variant: "destructive",
      })
      console.error('Error deleting employee:', error)
    }
  })

  const handleConfirmDelete = () => {
    if (employee) {
      deleteEmployeeMutation.mutate(employee.id)
    }
  }

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Xác nhận xóa nhân viên</AlertDialogTitle>
          <AlertDialogDescription>
            Hành động này sẽ xóa vĩnh viễn nhân viên "{employee?.fullName || employee?.email}" và không thể khôi phục. Bạn có chắc chắn muốn tiếp tục?
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Thoát</AlertDialogCancel>
          <AlertDialogAction onClick={handleConfirmDelete} className="bg-red-600 hover:bg-red-700">
            {deleteEmployeeMutation.isPending ? (
              <>
                <Spinner className="mr-2 h-4 w-4" />
                Đang xóa...
              </>
            ) : (
              'Xác nhận xóa'
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
} 