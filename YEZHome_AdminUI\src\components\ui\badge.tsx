// components/ui/badge.tsx
import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        subtle: "border-transparent", // Base for subtle variant, colors added via colorScheme
        outline: "text-foreground",
      },
      colorScheme: {
        blue: "bg-blue-100 text-blue-800",
        green: "bg-green-100 text-green-800",
        yellow: "bg-yellow-100 text-yellow-800",
        red: "bg-red-100 text-red-800",
        gray: "bg-gray-100 text-gray-800",
        purple: "bg-purple-100 text-purple-800",
        orange: "bg-orange-100 text-orange-800",
      },
      // Add a size for spacing with dot if needed, though gap-1 handles it well
      size: {
        default: "h-fit", // Keep existing padding
        withDot: "h-fit gap-1", // Add gap if dot is present
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default", // Default size without dot
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
      showDot?: boolean;
      dotColor?: 'blue' | 'green' | 'yellow' | 'red' | 'gray' | 'purple' | 'orange' | string;
      variant?: 'default' | 'secondary' | 'destructive' | 'outline' | 'subtle';
      colorScheme?: 'blue' | 'green' | 'yellow' | 'red' | 'gray' | 'purple' | 'orange';
    }

function Badge({
  className,
  variant,
  colorScheme, // New prop
  showDot, // New prop
  dotColor, // New prop
  ...props
}: BadgeProps) {

  // Determine the dot's color class
  const getDotColorClass = (scheme?: string) => {
    switch (scheme) {
      case 'blue': return 'bg-blue-500';
      case 'green': return 'bg-green-500';
      case 'yellow': return 'bg-yellow-500';
      case 'red': return 'bg-red-500';
      case 'gray': return 'bg-gray-500';
      case 'purple': return 'bg-purple-500';
      case 'orange': return 'bg-orange-500';
      default: return 'bg-current';
    }
  };

  const dotClassName = cn(
    "block h-2 w-2 rounded-full",
    getDotColorClass(dotColor || colorScheme)
  );

  return (
    <div
      className={cn(
        badgeVariants({
          variant,
          colorScheme: variant === 'subtle' ? colorScheme || 'gray' : undefined, 
          size: showDot ? 'withDot' : 'default',
        }),
        className
      )}
      {...props}
    >
      {showDot && <span className={dotClassName} />}
      {props.children}
    </div>
  )
}

export { Badge, badgeVariants }