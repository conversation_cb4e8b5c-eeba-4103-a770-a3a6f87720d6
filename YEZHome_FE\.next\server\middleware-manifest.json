{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_c570b039._.js", "server/edge/chunks/[root-of-the-server]__49146aab._.js", "server/edge/chunks/edge-wrapper_80d52818.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|_vercel|.*\\.png|.*\\.svg|.*\\.webp|.*\\.jpg$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|_vercel|.*\\.png|.*\\.svg|.*\\.webp|.*\\.jpg$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ibuBR8gd9czNjVKcVb89mS9lA91pSiY5k7/21W4re7I=", "__NEXT_PREVIEW_MODE_ID": "d4737985380a06fa5f7e172e51a27a68", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "909e9fc06372bed723ef860af46764c1f6e04ebf5bc780943907fc13c60f9ec7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f23a3bc557bc7d1039b1b33adc5b795e85672e8893d6fcbfc4b021a9b453665f"}}}, "sortedMiddleware": ["/"], "functions": {}}