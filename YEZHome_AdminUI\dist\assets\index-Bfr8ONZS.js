import{a as V,l as I,_ as L,r as o,b as B,m as O,W as b,Y as v,j as e,B as r,Z as Q,O as q,Q as K,S as m,T as S,P as X}from"./index-DFXNGaQ4.js";import{c as _,u as Z,h as G,i as J,T as U,a as W,b as x,d as Y,f as y,e as $,g as w}from"./table-BxBNKVGC.js";import{C as T,d as k,a as ee,b as se}from"./card-Dikj3j-6.js";import{I as j}from"./input-D3ozjVi3.js";import{L as f}from"./label-BscdIpbp.js";import{B as ae}from"./badge-BmdoMzu9.js";import{A as ne,a as ie,b as le,c as te,d as re,e as oe,f as ce,g as de}from"./alert-dialog-CINOfzqV.js";import{u as he,F as me}from"./form-lBragMB2.js";import{D as ge,a as ue,E as pe,b as xe,c as P}from"./dropdown-menu-DMTwZ3V-.js";import{E as je}from"./eye-DjtIFTil.js";import{T as fe}from"./trash-2-BZu0p0bP.js";import{S as Ce}from"./search-Crlu67m_.js";import"./index-BpksVOb4.js";q({page:S().optional().default(1),pageSize:S().optional().default(X),email:m().optional().default(""),name:m().optional().default(""),phone:m().optional().default(""),sortColumn:m().optional(),sortDescending:K().optional()});const ze=function(){const g=V(),M=I(),n=L.useSearch(),[D,u]=o.useState(!1),[c,C]=o.useState(null),p=o.useMemo(()=>({PageNumber:n.page,PageSize:n.pageSize,Email:n.email||void 0,Name:n.name||void 0,Phone:n.phone||void 0,SortColumn:n.sortColumn,SortDescending:n.sortDescending}),[n]),l=he({defaultValues:{email:n.email,name:n.name,phone:n.phone}}),{data:a,isLoading:E}=B({queryKey:["customers",p],queryFn:()=>b.getCustomers(p)}),A=O({mutationFn:s=>b.deleteUser(s),onSuccess:()=>{M.invalidateQueries({queryKey:["customers"]}),u(!1),C(null),v({title:"Thành công",description:"Đã xóa khách hàng",variant:"success"})},onError:s=>{v({title:"Lỗi",description:"Không thể xóa khách hàng. Vui lòng thử lại.",variant:"destructive"}),console.error("Error deleting customer:",s)}});o.useEffect(()=>{l.reset({email:n.email,name:n.name,phone:n.phone})},[n,l]);const R=s=>{g({to:"/customer",search:{...n,...s,page:1}})},z=s=>{g({to:"/customer/$customerId",params:{customerId:s.id},search:{from:"customer",filters:JSON.stringify(p)}})},F=s=>{C(s),u(!0)},H=()=>{c!=null&&c.id&&A.mutate(c.id)},t=_(),N=o.useMemo(()=>[t.accessor("fullName",{header:"Họ tên",cell:s=>s.getValue()||"-"}),t.accessor("email",{header:"Email",cell:s=>s.getValue()||"-"}),t.accessor("phone",{header:"Số điện thoại",cell:s=>s.getValue()||"-"}),t.accessor("userType",{header:"Loại người dùng",cell:s=>s.getValue()||"-"}),t.accessor("isActive",{header:"Trạng thái",cell:s=>{const i=s.getValue();return e.jsx(ae,{variant:"subtle",colorScheme:i?"green":"red",className:"whitespace-nowrap",children:i?"Đang hoạt động":"Đã vô hiệu hóa"})}}),t.accessor("lastLogin",{header:"Ngày đăng nhập cuối",cell:s=>{const i=s.getValue();return i?new Date(i).toLocaleDateString("vi-VN"):"-"}}),t.display({id:"actions",header:"",cell:s=>{const i=s.row.original;return e.jsx("div",{className:"text-right",children:e.jsxs(ge,{children:[e.jsx(ue,{asChild:!0,children:e.jsx(r,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:e.jsx(pe,{className:"h-4 w-4"})})}),e.jsxs(xe,{align:"end",children:[e.jsxs(P,{onClick:()=>z(i),children:[e.jsx(je,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:"Xem chi tiết"})]}),e.jsxs(P,{onClick:()=>F(i),className:"text-red-600",children:[e.jsx(fe,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:"Xóa"})]})]})]})})}})],[]),d=Z({data:(a==null?void 0:a.items)||[],columns:N,getCoreRowModel:J(),getPaginationRowModel:G(),initialState:{pagination:{pageSize:n.pageSize,pageIndex:n.page-1}},manualPagination:!0,pageCount:(a==null?void 0:a.pageCount)||-1}),h=s=>{g({to:"/customer",search:{...n,page:s+1}})};return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsx("h1",{className:"text-2xl font-bold",children:"Quản lý khách hàng"})}),e.jsx(T,{children:e.jsx(k,{children:e.jsx(me,{...l,children:e.jsxs("form",{onSubmit:l.handleSubmit(R),className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(f,{htmlFor:"email",children:"Email"}),e.jsx(j,{id:"email",placeholder:"Nhập email",...l.register("email")})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(f,{htmlFor:"name",children:"Họ tên"}),e.jsx(j,{id:"name",placeholder:"Nhập họ tên",...l.register("name")})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(f,{htmlFor:"phone",children:"Số điện thoại"}),e.jsx(j,{id:"phone",placeholder:"Nhập số điện thoại",...l.register("phone")})]})]}),e.jsx("div",{className:"flex justify-end",children:e.jsxs(r,{type:"submit",className:"flex items-center",children:[e.jsx(Ce,{className:"mr-2 h-4 w-4"}),"Tìm kiếm"]})})]})})})}),e.jsxs(T,{children:[e.jsx(ee,{children:e.jsx(se,{children:"Danh sách khách hàng"})}),e.jsx(k,{children:E?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(Q,{})}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"rounded-md border",children:e.jsxs(U,{children:[e.jsx(W,{className:"bg-muted[&_tr]:border-b bg-muted sticky top-0 z-10",children:d.getHeaderGroups().map(s=>e.jsx(x,{children:s.headers.map(i=>e.jsx(Y,{children:i.isPlaceholder?null:y(i.column.columnDef.header,i.getContext())},i.id))},s.id))}),e.jsx($,{children:d.getRowModel().rows.length>0?d.getRowModel().rows.map(s=>e.jsx(x,{children:s.getVisibleCells().map(i=>e.jsx(w,{children:y(i.column.columnDef.cell,i.getContext())},i.id))},s.id)):e.jsx(x,{children:e.jsx(w,{colSpan:N.length,className:"h-24 text-center",children:"Không có dữ liệu"})})})]})}),e.jsxs("div",{className:"flex items-center justify-between py-4",children:[e.jsxs("div",{className:"text-sm text-muted-foreground",children:["Hiển thị ",d.getRowModel().rows.length," trên ",(a==null?void 0:a.totalCount)||0," khách hàng"]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(r,{variant:"outline",size:"sm",onClick:()=>h(0),disabled:!(a!=null&&a.hasPreviousPage),children:"Đầu"}),e.jsx(r,{variant:"outline",size:"sm",onClick:()=>h(n.page-2),disabled:!(a!=null&&a.hasPreviousPage),children:"Trước"}),e.jsxs("span",{className:"text-sm",children:["Trang ",n.page," / ",(a==null?void 0:a.pageCount)||1]}),e.jsx(r,{variant:"outline",size:"sm",onClick:()=>h(n.page),disabled:!(a!=null&&a.hasNextPage),children:"Sau"}),e.jsx(r,{variant:"outline",size:"sm",onClick:()=>h(a?a.pageCount-1:0),disabled:!(a!=null&&a.hasNextPage),children:"Cuối"})]})]})]})})]}),e.jsx(ne,{open:D,onOpenChange:u,children:e.jsxs(ie,{children:[e.jsxs(le,{children:[e.jsx(te,{children:"Xác nhận xóa khách hàng"}),e.jsx(re,{children:"Bạn có chắc chắn muốn xóa khách hàng này? Hành động này không thể hoàn tác."})]}),e.jsxs(oe,{children:[e.jsx(ce,{children:"Thoát"}),e.jsx(de,{onClick:H,className:"bg-red-600 hover:bg-red-700",children:"Xác nhận xóa"})]})]})})]})};export{ze as component};
