import { create<PERSON>ile<PERSON>out<PERSON>, useNavigate, useRouter } from '@tanstack/react-router'
import React, { useState, useCallback } from 'react'
import { z } from 'zod'
import propertyService from '@/services/property-service'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { PropertyStatus, PropertyType, PostType, statusMap, propertyTypeMap, postTypeMap } from '@/lib/enum'
import { ArrowLeft, Calendar, Home, MapPin, User, DollarSign, Maximize2, Bed, Bath, Building2, Compass, Stamp, Clock, FileX, AlertTriangle } from 'lucide-react'
import { Separator } from '@/components/ui/separator'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialog<PERSON>ooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import DetailMap from '@/components/property/DetailMap'
import PropertyImageGallery from '@/components/property/PropertyImageGallery'
import type { PropertyMediaDto } from '@/lib/types/property'
import { Loading } from '@/components/ui/loading'

// Define search schema for the detail page
const propertyDetailSearchSchema = z.object({
  from: z.string().optional(),
  filters: z.string().optional(),
  mode: z.string().optional(),
})

export const Route = createFileRoute('/_authenticated/property/$propertyId')({
  validateSearch: propertyDetailSearchSchema,
  loader: async ({ params }) => {
    console.log(params);
    return await propertyService.getPropertyById(params.propertyId);
  },
  beforeLoad: () => {
    return {
      getTitle: () => 'Chi tiết bất động sản'
    };
  },
  component: PropertyDetail,
  pendingComponent: () => <Loading />,
  errorComponent: () => <div className="p-10">There was an error fetching data..</div>,
})

function PropertyDetail() {
  const data = Route.useLoaderData();
  const navigate = useNavigate();
  const search = Route.useSearch();
  const [activeTab, setActiveTab] = useState('overview');
  const [isApproveDialogOpen, setIsApproveDialogOpen] = useState(false);
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [localRejectReason, setLocalRejectReason] = useState('');
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { history } = useRouter();
  
  // Update rejectReason with debounce
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setRejectReason(localRejectReason);
    }, 100);
    
    return () => clearTimeout(timer);
  }, [localRejectReason]);
  
  // Check if property status is pending approval
  const [isPendingApproval, setIsPendingApproval] = useState(data.status === PropertyStatus.PendingApproval);
  
  // Use Tanstack Query to fetch property history
  const { data: historyData, isLoading: isLoadingHistory } = useQuery({
    queryKey: ['propertyHistory', data.id, activeTab],
    queryFn: async () => {
      if (activeTab !== 'history') return [];
      const response = await propertyService.getPropertyHistoryById(data.id);
      // Sort by date descending (latest first)
      return Array.isArray(response) 
        ? [...response].sort((a, b) => new Date(b.changedAt).getTime() - new Date(a.changedAt).getTime())
        : [];
    },
    enabled: activeTab === 'history' && !!data.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
  
  // Mutation for updating property status
  const updateStatusMutation = useMutation({
    mutationFn: async ({ status, comment }: { status: PropertyStatus, comment?: string }) => {
      return await propertyService.updatePropertyStatus(data.id, { status, comment });
    },
    onSuccess: () => {
      // Invalidate and refetch property data and history
      queryClient.invalidateQueries({ queryKey: ['propertyHistory', data.id] });
      
      // Show success toast
      toast({
        title: "Cập nhật trạng thái thành công",
        description: "Trạng thái bất động sản đã được cập nhật",
        variant: "success",
      });
      
      // Close dialogs
      setIsApproveDialogOpen(false);
      setIsRejectDialogOpen(false);
      setRejectReason('');
      setLocalRejectReason('');
      
      // Force refresh the current route to get updated data
      navigate({ to: `/property/${data.id}`, replace: true });
    },
    onError: (error) => {
      // Show error toast
      console.error("Error updating property status:", error);
      toast({
        title: "Lỗi cập nhật trạng thái",
        description: "Đã xảy ra lỗi khi cập nhật trạng thái bất động sản",
        variant: "destructive",
      });
    }
  });
  
  // Handle approve action
  const handleApprove = () => {
    updateStatusMutation.mutate({ status: PropertyStatus.Approved });
    // Immediately update local state
    setIsPendingApproval(false);
  };
  
  // Handle reject action
  const handleReject = () => {
    // Use the most current value (which might be the debounced one)
    const currentReason = localRejectReason || rejectReason;
    
    updateStatusMutation.mutate({ 
      status: PropertyStatus.RejectedByAdmin, 
      comment: currentReason 
    });
    
    // Immediately update local state
    setIsPendingApproval(false);
  };

  // Helper function to determine badge variant based on status
  const getStatusVariant = (status: PropertyStatus | string): "blue" | "green" | "yellow" | "red" | "gray" | "purple" | "orange" | undefined => {
    switch (status) {
      case PropertyStatus.Draft:
        return "gray";
      case PropertyStatus.PendingApproval:
        return "blue";
      case PropertyStatus.Approved:
        return "green";
      case PropertyStatus.RejectedByAdmin:
      case PropertyStatus.RejectedDueToUnpaid:
        return "red";
      case PropertyStatus.WaitingPayment:
        return "yellow";
      case PropertyStatus.Expired:
        return "gray";
      case PropertyStatus.Sold:
        return "purple";
      default:
        return "gray";
    }
  };

  let center = { latitude: data.latitude || 0, longitude: data.longitude || 0 };

  return (
    <div className="space-y-6">
      {/* Header with back button */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={() => {
            history.go(-1); 
          }}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">Chi tiết bất động sản</h1>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="subtle" colorScheme={getStatusVariant(data.status || '')} className="px-3 py-1 text-sm" showDot={true} dotColor={getStatusVariant(data.status || '')}>
            {statusMap[data.status as PropertyStatus] || data.status}
          </Badge>
        </div>
      </div>

      {/* Property basic info */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">{data.name}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span>{data.address || 'Chưa có địa chỉ'}</span>
              </div>

              <div className="flex items-center gap-2">
                <Home className="h-4 w-4 text-muted-foreground" />
                <span>
                  {propertyTypeMap[data.propertyType as PropertyType] || data.propertyType || 'Không xác định'}
                  {' - '}
                  {postTypeMap[data.postType as PostType] || data.postType || 'Không xác định'}
                </span>
              </div>

              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <span className="font-semibold">
                  {new Intl.NumberFormat('vi-VN', {
                    style: 'currency',
                    currency: 'VND',
                  }).format(data.price)}
                </span>
              </div>

              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span>
                  Ngày tạo: {new Date(data.createdAt).toLocaleDateString('vi-VN')}
                </span>
              </div>

              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span>
                  Ngày hết hạn: {new Date(data.expiresAt).toLocaleDateString('vi-VN')}
                </span>
              </div>

              <div className="flex items-center gap-2">
                <Stamp className="h-4 w-4 text-muted-foreground" />
                <Badge variant="subtle" colorScheme={getStatusVariant(data.status || '')} showDot={true} dotColor={getStatusVariant(data.status || '')}>
                  {statusMap[data.status as PropertyStatus] || data.status}
                </Badge>
              </div>
            </div>

            {/* Property media/propertyMedia */}
            <div className="rounded-md overflow-hidden h-64 bg-muted">
              {data.propertyMedia && data.propertyMedia.length > 0 ? (
                <img
                  src={data.propertyMedia[0].mediaURL}
                  alt={data.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="flex items-center justify-center h-full text-muted-foreground">
                  Không có hình ảnh
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Property details */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Left column - Property specifications */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Thông số chi tiết</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="flex flex-col items-center p-3 border rounded-md">
                <Maximize2 className="h-5 w-5 text-muted-foreground mb-2" />
                <span className="text-sm text-muted-foreground">Diện tích</span>
                <span className="font-medium">{data.area} m²</span>
              </div>

              <div className="flex flex-col items-center p-3 border rounded-md">
                <Bed className="h-5 w-5 text-muted-foreground mb-2" />
                <span className="text-sm text-muted-foreground">Phòng ngủ</span>
                <span className="font-medium">{data.rooms || 0}</span>
              </div>

              <div className="flex flex-col items-center p-3 border rounded-md">
                <Bath className="h-5 w-5 text-muted-foreground mb-2" />
                <span className="text-sm text-muted-foreground">Phòng tắm</span>
                <span className="font-medium">{data.toilets || 0}</span>
              </div>

              <div className="flex flex-col items-center p-3 border rounded-md">
                <Building2 className="h-5 w-5 text-muted-foreground mb-2" />
                <span className="text-sm text-muted-foreground">Số tầng</span>
                <span className="font-medium">{data.floors || 0}</span>
              </div>

              <div className="flex flex-col items-center p-3 border rounded-md">
                <Compass className="h-5 w-5 text-muted-foreground mb-2" />
                <span className="text-sm text-muted-foreground">Hướng</span>
                <span className="font-medium">{data.direction || 'Không xác định'}</span>
              </div>
            </div>

            <Separator className="my-6" />

            {/* Tabs for different sections */}
            <div className="space-y-6">
              <div className="flex border-b">
                <button
                  className={`px-4 py-2 font-medium ${activeTab === 'overview' ? 'border-b-2 border-primary' : 'text-muted-foreground'}`}
                  onClick={() => setActiveTab('overview')}
                >
                  Mô tả
                </button>
                <button
                  className={`px-4 py-2 font-medium ${activeTab === 'neighborhood' ? 'border-b-2 border-primary' : 'text-muted-foreground'}`}
                  onClick={() => setActiveTab('placeData')}
                >
                  Vị trí và khu vực
                </button>
                <button
                  className={`px-4 py-2 font-medium ${activeTab === 'policies' ? 'border-b-2 border-primary' : 'text-muted-foreground'}`}
                  onClick={() => setActiveTab('history')}
                >
                  Lịch sử thay đổi
                </button>
                <button
                  className={`px-4 py-2 font-medium ${activeTab === 'images' ? 'border-b-2 border-primary' : 'text-muted-foreground'}`}
                  onClick={() => setActiveTab('images')}
                >
                  Hình ảnh
                </button>
              </div>

              {activeTab === 'overview' && (
                <div className="prose max-w-none min-h-[500px]">
                  <p>{data.description || 'Chưa có thông tin tổng quan.'}</p>
                </div>
              )}

              {activeTab === 'placeData' && (
                <div className="prose max-w-none min-h-[500px]">
                  <DetailMap property={data} center={center} />
                </div>
              )}

              {activeTab === 'history' && (
                <div className="prose max-w-none min-h-[500px]">
                  {isLoadingHistory ? (
                    <div className="flex justify-center items-center h-64">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    </div>
                  ) : historyData && historyData.length > 0 ? (
                    <div className="space-y-6">
                      <h3 className="text-lg font-medium">Lịch sử thay đổi</h3>
                      <div className="relative border-l-2 border-gray-200 pl-6 space-y-6">
                        {historyData.map((item, index) => (
                          <div key={index} className="relative">
                            {/* Timeline dot */}
                            <div className="absolute -left-[29px] mt-1.5 h-4 w-4 rounded-full border-2 border-white bg-primary"></div>
                            <div className="mb-2 flex items-center gap-2">
                              <Clock className="h-4 w-4 text-muted-foreground" />
                              <time className="text-sm text-muted-foreground">
                                {new Date(item.changedAt).toLocaleString('vi-VN', {
                                  year: 'numeric',
                                  month: 'numeric',
                                  day: 'numeric',
                                  hour: '2-digit',
                                  minute: '2-digit'
                                })}
                              </time>
                            </div>
                            <div className="space-y-2">
                              <div className="flex items-center gap-2">
                                <span className="font-medium">Trạng thái:</span>
                                <Badge variant="subtle" colorScheme={getStatusVariant(item.status)} showDot={true} dotColor={getStatusVariant(item.status)}>
                                  {statusMap[item.status as PropertyStatus] || item.status}
                                </Badge>
                              </div>
                              {item.comment && (
                                <div>
                                  <span className="font-medium">Ghi chú:</span>
                                  <p className="text-sm mt-1">{item.comment}</p>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-64">
                      <p className="text-muted-foreground">Không có dữ liệu lịch sử</p>
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'images' && (
                <div className="prose max-w-none min-h-[500px]">
                  <p>{!data.propertyMedia || data.propertyMedia.length === 0 && 'Không có hình ảnh.'}</p>
                  {data.propertyMedia && data.propertyMedia.length > 0 && <PropertyImageGallery
                    images={data.propertyMedia.map((pm: PropertyMediaDto) => pm.mediaURL).filter((url): url is string => url !== undefined) || []}
                    propertyName={data.name}
                  />}
                </div>
              )}


            </div>
          </CardContent>
        </Card>

        {/* Right column - Owner info and status */}
        <div className="space-y-6">
          {/* Owner information */}
          <Card>
            <CardHeader>
              <CardTitle>Thông tin chủ sở hữu</CardTitle>
            </CardHeader>
            <CardContent>
              {data.owner ? (
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full bg-muted flex items-center justify-center">
                      <User className="h-5 w-5" />
                    </div>
                    <div>
                      <p className="font-medium">{data.owner.fullName}</p>
                      <p className="text-sm text-muted-foreground">{data.owner.email}</p>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Số điện thoại</span>
                      <span>{data.owner.phone}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Hạng thành viên</span>
                      <span>{data.owner.memberRank || 'Không có'}</span>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  Không có thông tin chủ sở hữu
                </div>
              )}
            </CardContent>
          </Card>
          
          {/* Approval actions */}
          <Card>
            <CardHeader>
              <CardTitle>Thao tác</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {isPendingApproval ? (
                  <>
                    <Button 
                      className="w-full" 
                      onClick={() => setIsApproveDialogOpen(true)}
                      disabled={updateStatusMutation.isPending}
                    >
                      {updateStatusMutation.isPending && updateStatusMutation.variables?.status === PropertyStatus.Approved ? (
                        <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"></div>
                      ) : (
                        <Stamp className="mr-2 h-4 w-4" />
                      )}
                      Duyệt
                    </Button>
                    <Button 
                      variant="destructive" 
                      className="w-full" 
                      onClick={() => setIsRejectDialogOpen(true)}
                      disabled={updateStatusMutation.isPending}
                    >
                      {updateStatusMutation.isPending && updateStatusMutation.variables?.status === PropertyStatus.RejectedByAdmin ? (
                        <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"></div>
                      ) : (
                        <FileX className="mr-2 h-4 w-4" />
                      )}
                      Không duyệt
                    </Button>
                  </>
                ) : (
                  <div className="flex items-center p-4 bg-muted/50 rounded-md">
                    <AlertTriangle className="h-5 w-5 text-amber-500 mr-2" />
                    <p className="text-sm">
                      Chỉ có thể thay đổi trạng thái khi bất động sản đang ở trạng thái chờ duyệt.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      
      {/* Approve Dialog */}
      <AlertDialog open={isApproveDialogOpen} onOpenChange={setIsApproveDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận duyệt</AlertDialogTitle>
            <AlertDialogDescription>
              Bạn có chắc chắn muốn duyệt bất động sản này không? Hành động này sẽ thay đổi trạng thái bất động sản thành "Đã duyệt".
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Hủy</AlertDialogCancel>
            <AlertDialogAction onClick={handleApprove} disabled={updateStatusMutation.isPending}>
              {updateStatusMutation.isPending && updateStatusMutation.variables?.status === PropertyStatus.Approved ? (
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"></div>
              ) : null}
              Xác nhận
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      {/* Reject Dialog */}
      <AlertDialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận từ chối</AlertDialogTitle>
            <AlertDialogDescription>
              Vui lòng nhập lý do từ chối duyệt bất động sản này.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="py-4">
            <Textarea
              placeholder="Nhập lý do từ chối..."
              value={localRejectReason}
              onChange={useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
                const value = e.target.value;
                setLocalRejectReason(value);
              }, [setLocalRejectReason])}
              className="min-h-[100px]"
            />
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>Hủy</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleReject} 
              disabled={updateStatusMutation.isPending || !localRejectReason.trim()}
            >
              {updateStatusMutation.isPending && updateStatusMutation.variables?.status === PropertyStatus.RejectedByAdmin ? (
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-b-transparent"></div>
              ) : null}
              Xác nhận
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
} 