import{ak as e}from"./index-DFXNGaQ4.js";class i{async getNotifications(t){const a=new URLSearchParams;if(t.page&&a.append("page",t.page.toString()),t.pageSize&&a.append("pageSize",t.pageSize.toString()),t.type&&a.append("type",t.type),t.startDate){const n=new Date(t.startDate).toISOString();a.append("startDate",n)}if(t.endDate){const n=new Date(t.endDate).toISOString();a.append("endDate",n)}return(await e.get(`/api/Notification?${a.toString()}`)).data}async getNotificationById(t){return(await e.get(`/api/Notification/${t}`)).data}async createNotification(t){return(await e.post("/api/Notification",t)).data}async deleteNotification(t){await e.delete(`/api/Notification/${t}`)}}const c=new i,r={SYSTEM:"Hệ thống",PROMOTION:"Khuyến mãi",NEWS:"Tin tức"};export{c as a,r as n};
