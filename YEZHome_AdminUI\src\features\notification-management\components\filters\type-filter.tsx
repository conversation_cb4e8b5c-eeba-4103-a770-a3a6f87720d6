import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ChevronDown } from 'lucide-react';
import { useState } from 'react';
import type { NotificationType } from '@/lib/types/notification';
import { notificationTypeMap } from '@/lib/types/notification';

interface TypeFilterProps {
  selectedType?: NotificationType;
  onTypeChange: (type: NotificationType | undefined) => void;
}

export const TypeFilter = ({ selectedType, onTypeChange }: TypeFilterProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const typeOptions = Object.entries(notificationTypeMap).map(([value, label]) => ({
    value: value as NotificationType,
    label,
  }));

  const handleTypeSelect = (type: NotificationType | undefined) => {
    onTypeChange(type);
    setIsOpen(false);
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" className="gap-2">
          Loại thông báo
          {selectedType && (
            <Badge variant="secondary" className="ml-1 px-1 py-0">
              1
            </Badge>
          )}
          <ChevronDown className="h-4 w-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64 p-4" align="start">
        <div className="space-y-2">
          <h4 className="font-medium">Loại thông báo</h4>
          <div className="space-y-2">
            <button
              onClick={() => handleTypeSelect(undefined)}
              className={`w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 ${
                !selectedType ? 'bg-blue-50 text-blue-700' : ''
              }`}
            >
              Tất cả
            </button>
            {typeOptions.map(option => (
              <button
                key={option.value}
                onClick={() => handleTypeSelect(option.value)}
                className={`w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 ${
                  selectedType === option.value ? 'bg-blue-50 text-blue-700' : ''
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}; 