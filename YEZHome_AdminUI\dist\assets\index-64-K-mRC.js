import{c as G,a as J,R as Q,r as x,b as P,j as e,p as M,d as z,s as h,B as m,P as X,e as l,f as U,g as W,L as I,h as g,i as R}from"./index-DFXNGaQ4.js";import{c as Z,u as Y,T as ee,a as se,b as u,d as ae,f as V,e as te,g as B,h as re,i as ne}from"./table-BxBNKVGC.js";import{C as y,d as j,a as le,b as ie}from"./card-Dikj3j-6.js";import{u as ce,F as oe}from"./form-lBragMB2.js";import{B as L}from"./badge-BmdoMzu9.js";import{P as pe,a as de,b as me}from"./popover-CUilP8_A.js";import{D as ge,a as he,E as xe,b as ue,c as N}from"./dropdown-menu-DMTwZ3V-.js";import{E as ye}from"./eye-DjtIFTil.js";import{S as je}from"./square-pen-U2EKTpxQ.js";import{T as Ne}from"./trash-2-BZu0p0bP.js";import{C as fe}from"./chevron-down-DuS99v9n.js";import"./label-BscdIpbp.js";import"./index-BpksVOb4.js";/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ve=[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]],be=G("Filter",ve),De=function(){var w,T,C;const d=J(),t=Q.useSearch(),o={page:t.page,pageSize:t.pageSize,postType:t.postType,propertyType:t.propertyType,status:t.status},[O,f]=x.useState(!1),{data:p,isLoading:k}=P({queryKey:["propertyCountStats"],queryFn:()=>R.getPropertyCountByStatus()}),{data:r,isLoading:D}=P({queryKey:["properties",o],queryFn:()=>R.searchProperties(o)}),i=ce({defaultValues:{postType:t.postType,propertyType:t.propertyType,status:t.status}});x.useEffect(()=>{i.reset({postType:t.postType,propertyType:t.propertyType,status:t.status})},[t,i]);const E=s=>{d({to:"/property",search:{...t,...s,page:1}}),f(!1)},F=s=>{d({to:"/property/$propertyId",params:{propertyId:s},search:{from:"property",filters:JSON.stringify(o)}})},H=s=>{d({to:"/property/$propertyId",params:{propertyId:s},search:{from:"property",filters:JSON.stringify(o),mode:"edit"}})},_=s=>{console.log("Delete property:",s)},c=Z(),v=x.useMemo(()=>[c.accessor("name",{header:"Tên",cell:s=>e.jsx("div",{className:"max-w-32 truncate font-medium sm:max-w-72 md:max-w-[31rem]",children:s.getValue()})}),c.accessor("propertyType",{header:"Loại BĐS",cell:s=>{const a=s.getValue();return M[a]||a||"-"}}),c.accessor("postType",{header:"Loại tin",cell:s=>{const a=s.getValue();return z[a]||a||"-"}}),c.accessor("address",{header:"Địa chỉ",cell:s=>s.getValue()||"-"}),c.accessor("price",{header:()=>e.jsx("div",{className:"text-right",children:"Giá"}),cell:s=>{const a=s.getValue();return e.jsx("div",{className:"text-right",children:new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(a)})}}),c.accessor("status",{header:"Trạng thái",cell:s=>{const a=s.getValue(),S=K(a);return e.jsx("div",{className:"w-24",children:e.jsx(L,{variant:"subtle",colorScheme:S,className:"whitespace-nowrap overflow-hidden text-ellipsis max-w-full",showDot:!0,dotColor:S,title:h[a]||a||"-",children:h[a]||a||"-"})})}}),c.accessor("createdAt",{header:"Ngày tạo",cell:s=>new Date(s.getValue()).toLocaleDateString("vi-VN")}),c.display({id:"actions",header:"",cell:s=>{const a=s.row.original.id;return e.jsx("div",{className:"text-right",children:e.jsxs(ge,{children:[e.jsx(he,{asChild:!0,children:e.jsx(m,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:e.jsx(xe,{className:"h-4 w-4"})})}),e.jsxs(ue,{align:"end",children:[e.jsxs(N,{onClick:()=>F(a),children:[e.jsx(ye,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:"Xem chi tiết"})]}),e.jsxs(N,{onClick:()=>H(a),children:[e.jsx(je,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:"Chỉnh sửa"})]}),e.jsxs(N,{onClick:()=>_(a),className:"text-red-600",children:[e.jsx(Ne,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:"Xóa"})]})]})]})})}})],[c,o]),n=Y({data:(r==null?void 0:r.items)||[],columns:v,getCoreRowModel:ne(),getPaginationRowModel:re(),pageCount:(r==null?void 0:r.pageCount)||-1,manualPagination:!0,state:{pagination:{pageIndex:(o.page||1)-1,pageSize:o.pageSize||X}},onPaginationChange:s=>{if(typeof s=="function"){const a=s(n.getState().pagination);d({to:"/property",search:{...t,page:a.pageIndex+1,pageSize:a.pageSize}})}else d({to:"/property",search:{...t,page:s.pageIndex+1,pageSize:s.pageSize}})}}),$=Object.entries(l).map(([s,a])=>({value:a,label:h[a]||a})),q=Object.entries(U).map(([s,a])=>({value:a,label:M[a]||a})),A=Object.entries(W).map(([s,a])=>({value:a,label:z[a]||a})),K=s=>{switch(s){case l.Draft:return"gray";case l.PendingApproval:return"blue";case l.Approved:return"green";case l.RejectedByAdmin:case l.RejectedDueToUnpaid:return"red";case l.WaitingPayment:return"yellow";case l.Expired:return"gray";case l.Sold:return"purple";default:return"gray"}},b=(((w=i.watch("propertyType"))==null?void 0:w.length)||0)+(((T=i.watch("postType"))==null?void 0:T.length)||0)+(((C=i.watch("status"))==null?void 0:C.length)||0);return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Quản lý bất động sản"}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(oe,{...i,children:e.jsxs(pe,{open:O,onOpenChange:f,children:[e.jsx(de,{asChild:!0,children:e.jsxs(m,{variant:"outline",className:"gap-2",children:[e.jsx(be,{className:"h-4 w-4"}),"Bộ lọc",b>0&&e.jsx(L,{variant:"secondary",className:"ml-1 px-1 py-0",children:b}),e.jsx(fe,{className:"h-4 w-4"})]})}),e.jsx(me,{className:"w-80 p-4",align:"end",children:e.jsxs("form",{onSubmit:i.handleSubmit(E),className:"grid gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"font-medium",children:"Loại BĐS"}),e.jsx("div",{className:"grid grid-cols-2 gap-2",children:q.map(s=>e.jsxs("label",{className:"flex items-center gap-1.5",children:[e.jsx("input",{type:"checkbox",value:s.value,...i.register("propertyType"),className:"h-4 w-4"}),e.jsx("span",{className:"text-sm",children:s.label})]},s.value))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"font-medium",children:"Loại tin"}),e.jsx("div",{className:"grid grid-cols-2 gap-2",children:A.map(s=>e.jsxs("label",{className:"flex items-center gap-1.5",children:[e.jsx("input",{type:"checkbox",value:s.value,...i.register("postType"),className:"h-4 w-4"}),e.jsx("span",{className:"text-sm",children:s.label})]},s.value))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"font-medium",children:"Trạng thái"}),e.jsx("div",{className:"grid grid-cols-2 gap-2",children:$.map(s=>e.jsxs("label",{className:"flex items-center gap-1.5",children:[e.jsx("input",{type:"checkbox",value:s.value,...i.register("status"),className:"h-4 w-4"}),e.jsx("span",{className:"text-sm",children:s.label})]},s.value))})]}),e.jsx(m,{type:"submit",className:"w-full",children:"Áp dụng bộ lọc"})]})})]})})})]}),k?e.jsx(I,{}):e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 xl:grid-cols-9 gap-4",children:[e.jsx(y,{className:"py-3",children:e.jsx(j,{className:"p-4",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:`text-2xl font-bold ${g[l.Draft].split(" ")[1]}`,children:(p==null?void 0:p.totalProperties)||0}),e.jsx("div",{className:`text-xs font-medium mt-1 ${g[l.Draft].split(" ")[1]} opacity-80`,children:"Tổng số"})]})})}),(p==null?void 0:p.propertiesByStatus)&&Object.entries(p.propertiesByStatus).map(([s,a])=>e.jsx(y,{className:`py-3 ${g[s]}`,children:e.jsx(j,{className:"p-4",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:`text-2xl font-bold ${g[s].split(" ")[1]}`,children:a}),e.jsx("div",{className:`text-xs font-medium mt-1 ${g[s].split(" ")[1]} opacity-80`,children:h[s]||s})]})})},s))]}),e.jsxs(y,{children:[e.jsx(le,{children:e.jsx(ie,{children:"Danh sách bất động sản"})}),e.jsx(j,{children:D?e.jsx(I,{}):e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"border rounded-md",children:e.jsxs(ee,{className:"w-full caption-bottom text-sm",children:[e.jsx(se,{className:"bg-muted[&_tr]:border-b bg-muted sticky top-0 z-10",children:n.getHeaderGroups().map(s=>e.jsx(u,{children:s.headers.map(a=>e.jsx(ae,{children:a.isPlaceholder?null:V(a.column.columnDef.header,a.getContext())},a.id))},s.id))}),e.jsx(te,{children:n.getRowModel().rows.length?n.getRowModel().rows.map(s=>e.jsx(u,{children:s.getVisibleCells().map(a=>e.jsx(B,{className:"whitespace-normal",children:V(a.column.columnDef.cell,a.getContext())},a.id))},s.id)):e.jsx(u,{children:e.jsx(B,{colSpan:v.length,className:"h-24 text-center",children:"Không có dữ liệu"})})})]})}),e.jsxs("div",{className:"flex items-center justify-between py-4",children:[e.jsxs("div",{className:"text-sm text-muted-foreground",children:["Hiển thị ",n.getState().pagination.pageIndex*n.getState().pagination.pageSize+1," đến"," ",Math.min((n.getState().pagination.pageIndex+1)*n.getState().pagination.pageSize,(r==null?void 0:r.totalCount)||0)," ","trong tổng số ",(r==null?void 0:r.totalCount)||0," kết quả"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(m,{variant:"outline",size:"sm",onClick:()=>n.previousPage(),disabled:!n.getCanPreviousPage(),children:"Trước"}),e.jsxs("span",{className:"text-sm",children:["Trang ",n.getState().pagination.pageIndex+1," / ",(r==null?void 0:r.pageCount)||1]}),e.jsx(m,{variant:"outline",size:"sm",onClick:()=>n.nextPage(),disabled:!n.getCanNextPage(),children:"Sau"})]})]})]})})]})]})};export{De as component};
