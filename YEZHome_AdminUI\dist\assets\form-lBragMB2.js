import{a8 as F,r as ne,j as se,N as Ae,aA as It}from"./index-DFXNGaQ4.js";import{L as Mt}from"./label-BscdIpbp.js";var ge=e=>e.type==="checkbox",ae=e=>e instanceof Date,N=e=>e==null;const ut=e=>typeof e=="object";var R=e=>!N(e)&&!Array.isArray(e)&&ut(e)&&!ae(e),dt=e=>R(e)&&e.target?ge(e.target)?e.target.checked:e.target.value:e,Ot=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,ft=(e,s)=>e.has(Ot(s)),Ut=e=>{const s=e.constructor&&e.constructor.prototype;return R(s)&&s.hasOwnProperty("isPrototypeOf")},Oe=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function M(e){let s;const t=Array.isArray(e),a=typeof FileList<"u"?e instanceof FileList:!1;if(e instanceof Date)s=new Date(e);else if(!(Oe&&(e instanceof Blob||a))&&(t||R(e)))if(s=t?[]:{},!t&&!Ut(e))s=e;else for(const n in e)e.hasOwnProperty(n)&&(s[n]=M(e[n]));else return e;return s}var we=e=>/^\w*$/.test(e),L=e=>e===void 0,Ue=e=>Array.isArray(e)?e.filter(Boolean):[],Ne=e=>Ue(e.replace(/["|']|\]/g,"").split(/\.|\[/)),y=(e,s,t)=>{if(!s||!R(e))return t;const a=(we(s)?[s]:Ne(s)).reduce((n,l)=>N(n)?n:n[l],e);return L(a)||a===e?L(e[s])?t:e[s]:a},q=e=>typeof e=="boolean",D=(e,s,t)=>{let a=-1;const n=we(s)?[s]:Ne(s),l=n.length,f=l-1;for(;++a<l;){const c=n[a];let b=t;if(a!==f){const O=e[c];b=R(O)||Array.isArray(O)?O:isNaN(+n[a+1])?{}:[]}if(c==="__proto__"||c==="constructor"||c==="prototype")return;e[c]=b,e=e[c]}};const _e={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},K={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},X={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},Pe=F.createContext(null);Pe.displayName="HookFormContext";const De=()=>F.useContext(Pe),Nt=e=>{const{children:s,...t}=e;return F.createElement(Pe.Provider,{value:t},s)};var ct=(e,s,t,a=!0)=>{const n={defaultValues:s._defaultValues};for(const l in e)Object.defineProperty(n,l,{get:()=>{const f=l;return s._proxyFormState[f]!==K.all&&(s._proxyFormState[f]=!a||K.all),t&&(t[f]=!0),e[f]}});return n};const Be=typeof window<"u"?ne.useLayoutEffect:ne.useEffect;function yt(e){const s=De(),{control:t=s.control,disabled:a,name:n,exact:l}=e||{},[f,c]=F.useState(t._formState),b=F.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return Be(()=>t._subscribe({name:n,formState:b.current,exact:l,callback:O=>{!a&&c({...t._formState,...O})}}),[n,a,l]),F.useEffect(()=>{b.current.isValid&&t._setValid(!0)},[t]),F.useMemo(()=>ct(f,t,b.current,!1),[f,t])}var J=e=>typeof e=="string",gt=(e,s,t,a,n)=>J(e)?(a&&s.watch.add(e),y(t,e,n)):Array.isArray(e)?e.map(l=>(a&&s.watch.add(l),y(t,l))):(a&&(s.watchAll=!0),t);function Pt(e){const s=De(),{control:t=s.control,name:a,defaultValue:n,disabled:l,exact:f}=e||{},c=F.useRef(n),[b,O]=F.useState(t._getWatch(a,c.current));return Be(()=>t._subscribe({name:a,formState:{values:!0},exact:f,callback:V=>!l&&O(gt(a,t._names,V.values||t._formValues,!1,c.current))}),[a,t,l,f]),F.useEffect(()=>t._removeUnmounted()),b}function Bt(e){const s=De(),{name:t,disabled:a,control:n=s.control,shouldUnregister:l}=e,f=ft(n._names.array,t),c=Pt({control:n,name:t,defaultValue:y(n._formValues,t,y(n._defaultValues,t,e.defaultValue)),exact:!0}),b=yt({control:n,name:t,exact:!0}),O=F.useRef(e),V=F.useRef(n.register(t,{...e.rules,value:c,...q(e.disabled)?{disabled:e.disabled}:{}})),S=F.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!y(b.errors,t)},isDirty:{enumerable:!0,get:()=>!!y(b.dirtyFields,t)},isTouched:{enumerable:!0,get:()=>!!y(b.touchedFields,t)},isValidating:{enumerable:!0,get:()=>!!y(b.validatingFields,t)},error:{enumerable:!0,get:()=>y(b.errors,t)}}),[b,t]),m=F.useCallback(k=>V.current.onChange({target:{value:dt(k),name:t},type:_e.CHANGE}),[t]),Z=F.useCallback(()=>V.current.onBlur({target:{value:y(n._formValues,t),name:t},type:_e.BLUR}),[t,n._formValues]),H=F.useCallback(k=>{const Q=y(n._fields,t);Q&&k&&(Q._f.ref={focus:()=>k.focus&&k.focus(),select:()=>k.select&&k.select(),setCustomValidity:v=>k.setCustomValidity(v),reportValidity:()=>k.reportValidity()})},[n._fields,t]),E=F.useMemo(()=>({name:t,value:c,...q(a)||b.disabled?{disabled:b.disabled||a}:{},onChange:m,onBlur:Z,ref:H}),[t,a,b.disabled,m,Z,H,c]);return F.useEffect(()=>{const k=n._options.shouldUnregister||l;n.register(t,{...O.current.rules,...q(O.current.disabled)?{disabled:O.current.disabled}:{}});const Q=(v,$)=>{const P=y(n._fields,v);P&&P._f&&(P._f.mount=$)};if(Q(t,!0),k){const v=M(y(n._options.defaultValues,t));D(n._defaultValues,t,v),L(y(n._formValues,t))&&D(n._formValues,t,v)}return!f&&n.register(t),()=>{(f?k&&!n._state.action:k)?n.unregister(t):Q(t,!1)}},[t,n,f,l]),F.useEffect(()=>{n._setDisabledField({disabled:a,name:t})},[a,t,n]),F.useMemo(()=>({field:E,formState:b,fieldState:S}),[E,b,S])}const Wt=e=>e.render(Bt(e));var qt=(e,s,t,a,n)=>s?{...t[e],types:{...t[e]&&t[e].types?t[e].types:{},[a]:n||!0}}:{},ce=e=>Array.isArray(e)?e:[e],Xe=()=>{let e=[];return{get observers(){return e},next:n=>{for(const l of e)l.next&&l.next(n)},subscribe:n=>(e.push(n),{unsubscribe:()=>{e=e.filter(l=>l!==n)}}),unsubscribe:()=>{e=[]}}},Me=e=>N(e)||!ut(e);function re(e,s,t=new WeakSet){if(Me(e)||Me(s))return e===s;if(ae(e)&&ae(s))return e.getTime()===s.getTime();const a=Object.keys(e),n=Object.keys(s);if(a.length!==n.length)return!1;if(t.has(e)||t.has(s))return!0;t.add(e),t.add(s);for(const l of a){const f=e[l];if(!n.includes(l))return!1;if(l!=="ref"){const c=s[l];if(ae(f)&&ae(c)||R(f)&&R(c)||Array.isArray(f)&&Array.isArray(c)?!re(f,c,t):f!==c)return!1}}return!0}var W=e=>R(e)&&!Object.keys(e).length,We=e=>e.type==="file",z=e=>typeof e=="function",Fe=e=>{if(!Oe)return!1;const s=e?e.ownerDocument:0;return e instanceof(s&&s.defaultView?s.defaultView.HTMLElement:HTMLElement)},ht=e=>e.type==="select-multiple",qe=e=>e.type==="radio",pt=e=>qe(e)||ge(e),Ie=e=>Fe(e)&&e.isConnected;function Ht(e,s){const t=s.slice(0,-1).length;let a=0;for(;a<t;)e=L(e)?a++:e[s[a++]];return e}function $t(e){for(const s in e)if(e.hasOwnProperty(s)&&!L(e[s]))return!1;return!0}function T(e,s){const t=Array.isArray(s)?s:we(s)?[s]:Ne(s),a=t.length===1?e:Ht(e,t),n=t.length-1,l=t[n];return a&&delete a[l],n!==0&&(R(a)&&W(a)||Array.isArray(a)&&$t(a))&&T(e,t.slice(0,-1)),e}var mt=e=>{for(const s in e)if(z(e[s]))return!0;return!1};function Ve(e,s={}){const t=Array.isArray(e);if(R(e)||t)for(const a in e)Array.isArray(e[a])||R(e[a])&&!mt(e[a])?(s[a]=Array.isArray(e[a])?[]:{},Ve(e[a],s[a])):N(e[a])||(s[a]=!0);return s}function vt(e,s,t){const a=Array.isArray(e);if(R(e)||a)for(const n in e)Array.isArray(e[n])||R(e[n])&&!mt(e[n])?L(s)||Me(t[n])?t[n]=Array.isArray(e[n])?Ve(e[n],[]):{...Ve(e[n])}:vt(e[n],N(s)?{}:s[n],t[n]):t[n]=!re(e[n],s[n]);return t}var de=(e,s)=>vt(e,s,Ve(s));const Ze={value:!1,isValid:!1},et={value:!0,isValid:!0};var bt=e=>{if(Array.isArray(e)){if(e.length>1){const s=e.filter(t=>t&&t.checked&&!t.disabled).map(t=>t.value);return{value:s,isValid:!!s.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!L(e[0].attributes.value)?L(e[0].value)||e[0].value===""?et:{value:e[0].value,isValid:!0}:et:Ze}return Ze},_t=(e,{valueAsNumber:s,valueAsDate:t,setValueAs:a})=>L(e)?e:s?e===""?NaN:e&&+e:t&&J(e)?new Date(e):a?a(e):e;const tt={isValid:!1,value:null};var Ft=e=>Array.isArray(e)?e.reduce((s,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:s,tt):tt;function rt(e){const s=e.ref;return We(s)?s.files:qe(s)?Ft(e.refs).value:ht(s)?[...s.selectedOptions].map(({value:t})=>t):ge(s)?bt(e.refs).value:_t(L(s.value)?e.ref.value:s.value,e)}var jt=(e,s,t,a)=>{const n={};for(const l of e){const f=y(s,l);f&&D(n,l,f._f)}return{criteriaMode:t,names:[...e],fields:n,shouldUseNativeValidation:a}},xe=e=>e instanceof RegExp,fe=e=>L(e)?e:xe(e)?e.source:R(e)?xe(e.value)?e.value.source:e.value:e,st=e=>({isOnSubmit:!e||e===K.onSubmit,isOnBlur:e===K.onBlur,isOnChange:e===K.onChange,isOnAll:e===K.all,isOnTouch:e===K.onTouched});const it="AsyncFunction";var Kt=e=>!!e&&!!e.validate&&!!(z(e.validate)&&e.validate.constructor.name===it||R(e.validate)&&Object.values(e.validate).find(s=>s.constructor.name===it)),zt=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),at=(e,s,t)=>!t&&(s.watchAll||s.watch.has(e)||[...s.watch].some(a=>e.startsWith(a)&&/^\.\w+/.test(e.slice(a.length))));const ye=(e,s,t,a)=>{for(const n of t||Object.keys(e)){const l=y(e,n);if(l){const{_f:f,...c}=l;if(f){if(f.refs&&f.refs[0]&&s(f.refs[0],n)&&!a)return!0;if(f.ref&&s(f.ref,f.name)&&!a)return!0;if(ye(c,s))break}else if(R(c)&&ye(c,s))break}}};function nt(e,s,t){const a=y(e,t);if(a||we(t))return{error:a,name:t};const n=t.split(".");for(;n.length;){const l=n.join("."),f=y(s,l),c=y(e,l);if(f&&!Array.isArray(f)&&t!==l)return{name:t};if(c&&c.type)return{name:l,error:c};if(c&&c.root&&c.root.type)return{name:`${l}.root`,error:c.root};n.pop()}return{name:t}}var Gt=(e,s,t,a)=>{t(e);const{name:n,...l}=e;return W(l)||Object.keys(l).length>=Object.keys(s).length||Object.keys(l).find(f=>s[f]===(!a||K.all))},Yt=(e,s,t)=>!e||!s||e===s||ce(e).some(a=>a&&(t?a===s:a.startsWith(s)||s.startsWith(a))),Jt=(e,s,t,a,n)=>n.isOnAll?!1:!t&&n.isOnTouch?!(s||e):(t?a.isOnBlur:n.isOnBlur)?!e:(t?a.isOnChange:n.isOnChange)?e:!0,Qt=(e,s)=>!Ue(y(e,s)).length&&T(e,s),Xt=(e,s,t)=>{const a=ce(y(e,t));return D(a,"root",s[t]),D(e,t,a),e},be=e=>J(e);function lt(e,s,t="validate"){if(be(e)||Array.isArray(e)&&e.every(be)||q(e)&&!e)return{type:t,message:be(e)?e:"",ref:s}}var oe=e=>R(e)&&!xe(e)?e:{value:e,message:""},ot=async(e,s,t,a,n,l)=>{const{ref:f,refs:c,required:b,maxLength:O,minLength:V,min:S,max:m,pattern:Z,validate:H,name:E,valueAsNumber:k,mount:Q}=e._f,v=y(t,E);if(!Q||s.has(E))return{};const $=c?c[0]:f,P=_=>{n&&$.reportValidity&&($.setCustomValidity(q(_)?"":_||""),$.reportValidity())},I={},he=qe(f),ee=ge(f),Ee=he||ee,j=(k||We(f))&&L(f.value)&&L(v)||Fe(f)&&f.value===""||v===""||Array.isArray(v)&&!v.length,ie=qt.bind(null,E,a,I),G=(_,A,C,U=X.maxLength,B=X.minLength)=>{const Y=_?A:C;I[E]={type:_?U:B,message:Y,ref:f,...ie(_?U:B,Y)}};if(l?!Array.isArray(v)||!v.length:b&&(!Ee&&(j||N(v))||q(v)&&!v||ee&&!bt(c).isValid||he&&!Ft(c).isValid)){const{value:_,message:A}=be(b)?{value:!!b,message:b}:oe(b);if(_&&(I[E]={type:X.required,message:A,ref:$,...ie(X.required,A)},!a))return P(A),I}if(!j&&(!N(S)||!N(m))){let _,A;const C=oe(m),U=oe(S);if(!N(v)&&!isNaN(v)){const B=f.valueAsNumber||v&&+v;N(C.value)||(_=B>C.value),N(U.value)||(A=B<U.value)}else{const B=f.valueAsDate||new Date(v),Y=me=>new Date(new Date().toDateString()+" "+me),ue=f.type=="time",le=f.type=="week";J(C.value)&&v&&(_=ue?Y(v)>Y(C.value):le?v>C.value:B>new Date(C.value)),J(U.value)&&v&&(A=ue?Y(v)<Y(U.value):le?v<U.value:B<new Date(U.value))}if((_||A)&&(G(!!_,C.message,U.message,X.max,X.min),!a))return P(I[E].message),I}if((O||V)&&!j&&(J(v)||l&&Array.isArray(v))){const _=oe(O),A=oe(V),C=!N(_.value)&&v.length>+_.value,U=!N(A.value)&&v.length<+A.value;if((C||U)&&(G(C,_.message,A.message),!a))return P(I[E].message),I}if(Z&&!j&&J(v)){const{value:_,message:A}=oe(Z);if(xe(_)&&!v.match(_)&&(I[E]={type:X.pattern,message:A,ref:f,...ie(X.pattern,A)},!a))return P(A),I}if(H){if(z(H)){const _=await H(v,t),A=lt(_,$);if(A&&(I[E]={...A,...ie(X.validate,A.message)},!a))return P(A.message),I}else if(R(H)){let _={};for(const A in H){if(!W(_)&&!a)break;const C=lt(await H[A](v,t),$,A);C&&(_={...C,...ie(A,C.message)},P(C.message),a&&(I[E]=_))}if(!W(_)&&(I[E]={ref:$,..._},!a))return I}}return P(!0),I};const Zt={mode:K.onSubmit,reValidateMode:K.onChange,shouldFocusError:!0};function er(e={}){let s={...Zt,...e},t={submitCount:0,isDirty:!1,isReady:!1,isLoading:z(s.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:s.errors||{},disabled:s.disabled||!1},a={},n=R(s.defaultValues)||R(s.values)?M(s.defaultValues||s.values)||{}:{},l=s.shouldUnregister?{}:M(n),f={action:!1,mount:!1,watch:!1},c={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},b,O=0;const V={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let S={...V};const m={array:Xe(),state:Xe()},Z=s.criteriaMode===K.all,H=r=>i=>{clearTimeout(O),O=setTimeout(r,i)},E=async r=>{if(!s.disabled&&(V.isValid||S.isValid||r)){const i=s.resolver?W((await ee()).errors):await j(a,!0);i!==t.isValid&&m.state.next({isValid:i})}},k=(r,i)=>{!s.disabled&&(V.isValidating||V.validatingFields||S.isValidating||S.validatingFields)&&((r||Array.from(c.mount)).forEach(o=>{o&&(i?D(t.validatingFields,o,i):T(t.validatingFields,o))}),m.state.next({validatingFields:t.validatingFields,isValidating:!W(t.validatingFields)}))},Q=(r,i=[],o,g,d=!0,u=!0)=>{if(g&&o&&!s.disabled){if(f.action=!0,u&&Array.isArray(y(a,r))){const h=o(y(a,r),g.argA,g.argB);d&&D(a,r,h)}if(u&&Array.isArray(y(t.errors,r))){const h=o(y(t.errors,r),g.argA,g.argB);d&&D(t.errors,r,h),Qt(t.errors,r)}if((V.touchedFields||S.touchedFields)&&u&&Array.isArray(y(t.touchedFields,r))){const h=o(y(t.touchedFields,r),g.argA,g.argB);d&&D(t.touchedFields,r,h)}(V.dirtyFields||S.dirtyFields)&&(t.dirtyFields=de(n,l)),m.state.next({name:r,isDirty:G(r,i),dirtyFields:t.dirtyFields,errors:t.errors,isValid:t.isValid})}else D(l,r,i)},v=(r,i)=>{D(t.errors,r,i),m.state.next({errors:t.errors})},$=r=>{t.errors=r,m.state.next({errors:t.errors,isValid:!1})},P=(r,i,o,g)=>{const d=y(a,r);if(d){const u=y(l,r,L(o)?y(n,r):o);L(u)||g&&g.defaultChecked||i?D(l,r,i?u:rt(d._f)):C(r,u),f.mount&&E()}},I=(r,i,o,g,d)=>{let u=!1,h=!1;const x={name:r};if(!s.disabled){if(!o||g){(V.isDirty||S.isDirty)&&(h=t.isDirty,t.isDirty=x.isDirty=G(),u=h!==x.isDirty);const w=re(y(n,r),i);h=!!y(t.dirtyFields,r),w?T(t.dirtyFields,r):D(t.dirtyFields,r,!0),x.dirtyFields=t.dirtyFields,u=u||(V.dirtyFields||S.dirtyFields)&&h!==!w}if(o){const w=y(t.touchedFields,r);w||(D(t.touchedFields,r,o),x.touchedFields=t.touchedFields,u=u||(V.touchedFields||S.touchedFields)&&w!==o)}u&&d&&m.state.next(x)}return u?x:{}},he=(r,i,o,g)=>{const d=y(t.errors,r),u=(V.isValid||S.isValid)&&q(i)&&t.isValid!==i;if(s.delayError&&o?(b=H(()=>v(r,o)),b(s.delayError)):(clearTimeout(O),b=null,o?D(t.errors,r,o):T(t.errors,r)),(o?!re(d,o):d)||!W(g)||u){const h={...g,...u&&q(i)?{isValid:i}:{},errors:t.errors,name:r};t={...t,...h},m.state.next(h)}},ee=async r=>{k(r,!0);const i=await s.resolver(l,s.context,jt(r||c.mount,a,s.criteriaMode,s.shouldUseNativeValidation));return k(r),i},Ee=async r=>{const{errors:i}=await ee(r);if(r)for(const o of r){const g=y(i,o);g?D(t.errors,o,g):T(t.errors,o)}else t.errors=i;return i},j=async(r,i,o={valid:!0})=>{for(const g in r){const d=r[g];if(d){const{_f:u,...h}=d;if(u){const x=c.array.has(u.name),w=d._f&&Kt(d._f);w&&V.validatingFields&&k([g],!0);const p=await ot(d,c.disabled,l,Z,s.shouldUseNativeValidation&&!i,x);if(w&&V.validatingFields&&k([g]),p[u.name]&&(o.valid=!1,i))break;!i&&(y(p,u.name)?x?Xt(t.errors,p,u.name):D(t.errors,u.name,p[u.name]):T(t.errors,u.name))}!W(h)&&await j(h,i,o)}}return o.valid},ie=()=>{for(const r of c.unMount){const i=y(a,r);i&&(i._f.refs?i._f.refs.every(o=>!Ie(o)):!Ie(i._f.ref))&&ke(r)}c.unMount=new Set},G=(r,i)=>!s.disabled&&(r&&i&&D(l,r,i),!re(me(),n)),_=(r,i,o)=>gt(r,c,{...f.mount?l:L(i)?n:J(r)?{[r]:i}:i},o,i),A=r=>Ue(y(f.mount?l:n,r,s.shouldUnregister?y(n,r,[]):[])),C=(r,i,o={})=>{const g=y(a,r);let d=i;if(g){const u=g._f;u&&(!u.disabled&&D(l,r,_t(i,u)),d=Fe(u.ref)&&N(i)?"":i,ht(u.ref)?[...u.ref.options].forEach(h=>h.selected=d.includes(h.value)):u.refs?ge(u.ref)?u.refs.forEach(h=>{(!h.defaultChecked||!h.disabled)&&(Array.isArray(d)?h.checked=!!d.find(x=>x===h.value):h.checked=d===h.value||!!d)}):u.refs.forEach(h=>h.checked=h.value===d):We(u.ref)?u.ref.value="":(u.ref.value=d,u.ref.type||m.state.next({name:r,values:M(l)})))}(o.shouldDirty||o.shouldTouch)&&I(r,d,o.shouldTouch,o.shouldDirty,!0),o.shouldValidate&&le(r)},U=(r,i,o)=>{for(const g in i){if(!i.hasOwnProperty(g))return;const d=i[g],u=r+"."+g,h=y(a,u);(c.array.has(r)||R(d)||h&&!h._f)&&!ae(d)?U(u,d,o):C(u,d,o)}},B=(r,i,o={})=>{const g=y(a,r),d=c.array.has(r),u=M(i);D(l,r,u),d?(m.array.next({name:r,values:M(l)}),(V.isDirty||V.dirtyFields||S.isDirty||S.dirtyFields)&&o.shouldDirty&&m.state.next({name:r,dirtyFields:de(n,l),isDirty:G(r,u)})):g&&!g._f&&!N(u)?U(r,u,o):C(r,u,o),at(r,c)&&m.state.next({...t}),m.state.next({name:f.mount?r:void 0,values:M(l)})},Y=async r=>{f.mount=!0;const i=r.target;let o=i.name,g=!0;const d=y(a,o),u=w=>{g=Number.isNaN(w)||ae(w)&&isNaN(w.getTime())||re(w,y(l,o,w))},h=st(s.mode),x=st(s.reValidateMode);if(d){let w,p;const ve=i.type?rt(d._f):dt(r),te=r.type===_e.BLUR||r.type===_e.FOCUS_OUT,Lt=!zt(d._f)&&!s.resolver&&!y(t.errors,o)&&!d._f.deps||Jt(te,y(t.touchedFields,o),t.isSubmitted,x,h),Re=at(o,c,te);D(l,o,ve),te?(d._f.onBlur&&d._f.onBlur(r),b&&b(0)):d._f.onChange&&d._f.onChange(r);const Te=I(o,ve,te),Rt=!W(Te)||Re;if(!te&&m.state.next({name:o,type:r.type,values:M(l)}),Lt)return(V.isValid||S.isValid)&&(s.mode==="onBlur"?te&&E():te||E()),Rt&&m.state.next({name:o,...Re?{}:Te});if(!te&&Re&&m.state.next({...t}),s.resolver){const{errors:Je}=await ee([o]);if(u(ve),g){const Tt=nt(t.errors,a,o),Qe=nt(Je,a,Tt.name||o);w=Qe.error,o=Qe.name,p=W(Je)}}else k([o],!0),w=(await ot(d,c.disabled,l,Z,s.shouldUseNativeValidation))[o],k([o]),u(ve),g&&(w?p=!1:(V.isValid||S.isValid)&&(p=await j(a,!0)));g&&(d._f.deps&&le(d._f.deps),he(o,p,w,Te))}},ue=(r,i)=>{if(y(t.errors,i)&&r.focus)return r.focus(),1},le=async(r,i={})=>{let o,g;const d=ce(r);if(s.resolver){const u=await Ee(L(r)?r:d);o=W(u),g=r?!d.some(h=>y(u,h)):o}else r?(g=(await Promise.all(d.map(async u=>{const h=y(a,u);return await j(h&&h._f?{[u]:h}:h)}))).every(Boolean),!(!g&&!t.isValid)&&E()):g=o=await j(a);return m.state.next({...!J(r)||(V.isValid||S.isValid)&&o!==t.isValid?{}:{name:r},...s.resolver||!r?{isValid:o}:{},errors:t.errors}),i.shouldFocus&&!g&&ye(a,ue,r?d:c.mount),g},me=r=>{const i={...f.mount?l:n};return L(r)?i:J(r)?y(i,r):r.map(o=>y(i,o))},pe=(r,i)=>({invalid:!!y((i||t).errors,r),isDirty:!!y((i||t).dirtyFields,r),error:y((i||t).errors,r),isValidating:!!y(t.validatingFields,r),isTouched:!!y((i||t).touchedFields,r)}),At=r=>{r&&ce(r).forEach(i=>T(t.errors,i)),m.state.next({errors:r?t.errors:{}})},He=(r,i,o)=>{const g=(y(a,r,{_f:{}})._f||{}).ref,d=y(t.errors,r)||{},{ref:u,message:h,type:x,...w}=d;D(t.errors,r,{...w,...i,ref:g}),m.state.next({name:r,errors:t.errors,isValid:!1}),o&&o.shouldFocus&&g&&g.focus&&g.focus()},wt=(r,i)=>z(r)?m.state.subscribe({next:o=>r(_(void 0,i),o)}):_(r,i,!0),$e=r=>m.state.subscribe({next:i=>{Yt(r.name,i.name,r.exact)&&Gt(i,r.formState||V,Ct,r.reRenderRoot)&&r.callback({values:{...l},...t,...i})}}).unsubscribe,Dt=r=>(f.mount=!0,S={...S,...r.formState},$e({...r,formState:S})),ke=(r,i={})=>{for(const o of r?ce(r):c.mount)c.mount.delete(o),c.array.delete(o),i.keepValue||(T(a,o),T(l,o)),!i.keepError&&T(t.errors,o),!i.keepDirty&&T(t.dirtyFields,o),!i.keepTouched&&T(t.touchedFields,o),!i.keepIsValidating&&T(t.validatingFields,o),!s.shouldUnregister&&!i.keepDefaultValue&&T(n,o);m.state.next({values:M(l)}),m.state.next({...t,...i.keepDirty?{isDirty:G()}:{}}),!i.keepIsValid&&E()},je=({disabled:r,name:i})=>{(q(r)&&f.mount||r||c.disabled.has(i))&&(r?c.disabled.add(i):c.disabled.delete(i))},Ce=(r,i={})=>{let o=y(a,r);const g=q(i.disabled)||q(s.disabled);return D(a,r,{...o||{},_f:{...o&&o._f?o._f:{ref:{name:r}},name:r,mount:!0,...i}}),c.mount.add(r),o?je({disabled:q(i.disabled)?i.disabled:s.disabled,name:r}):P(r,!0,i.value),{...g?{disabled:i.disabled||s.disabled}:{},...s.progressive?{required:!!i.required,min:fe(i.min),max:fe(i.max),minLength:fe(i.minLength),maxLength:fe(i.maxLength),pattern:fe(i.pattern)}:{},name:r,onChange:Y,onBlur:Y,ref:d=>{if(d){Ce(r,i),o=y(a,r);const u=L(d.value)&&d.querySelectorAll&&d.querySelectorAll("input,select,textarea")[0]||d,h=pt(u),x=o._f.refs||[];if(h?x.find(w=>w===u):u===o._f.ref)return;D(a,r,{_f:{...o._f,...h?{refs:[...x.filter(Ie),u,...Array.isArray(y(n,r))?[{}]:[]],ref:{type:u.type,name:r}}:{ref:u}}}),P(r,!1,void 0,u)}else o=y(a,r,{}),o._f&&(o._f.mount=!1),(s.shouldUnregister||i.shouldUnregister)&&!(ft(c.array,r)&&f.action)&&c.unMount.add(r)}}},Le=()=>s.shouldFocusError&&ye(a,ue,c.mount),St=r=>{q(r)&&(m.state.next({disabled:r}),ye(a,(i,o)=>{const g=y(a,o);g&&(i.disabled=g._f.disabled||r,Array.isArray(g._f.refs)&&g._f.refs.forEach(d=>{d.disabled=g._f.disabled||r}))},0,!1))},Ke=(r,i)=>async o=>{let g;o&&(o.preventDefault&&o.preventDefault(),o.persist&&o.persist());let d=M(l);if(m.state.next({isSubmitting:!0}),s.resolver){const{errors:u,values:h}=await ee();t.errors=u,d=M(h)}else await j(a);if(c.disabled.size)for(const u of c.disabled)T(d,u);if(T(t.errors,"root"),W(t.errors)){m.state.next({errors:{}});try{await r(d,o)}catch(u){g=u}}else i&&await i({...t.errors},o),Le(),setTimeout(Le);if(m.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:W(t.errors)&&!g,submitCount:t.submitCount+1,errors:t.errors}),g)throw g},Et=(r,i={})=>{y(a,r)&&(L(i.defaultValue)?B(r,M(y(n,r))):(B(r,i.defaultValue),D(n,r,M(i.defaultValue))),i.keepTouched||T(t.touchedFields,r),i.keepDirty||(T(t.dirtyFields,r),t.isDirty=i.defaultValue?G(r,M(y(n,r))):G()),i.keepError||(T(t.errors,r),V.isValid&&E()),m.state.next({...t}))},ze=(r,i={})=>{const o=r?M(r):n,g=M(o),d=W(r),u=d?n:g;if(i.keepDefaultValues||(n=o),!i.keepValues){if(i.keepDirtyValues){const h=new Set([...c.mount,...Object.keys(de(n,l))]);for(const x of Array.from(h))y(t.dirtyFields,x)?D(u,x,y(l,x)):B(x,y(u,x))}else{if(Oe&&L(r))for(const h of c.mount){const x=y(a,h);if(x&&x._f){const w=Array.isArray(x._f.refs)?x._f.refs[0]:x._f.ref;if(Fe(w)){const p=w.closest("form");if(p){p.reset();break}}}}if(i.keepFieldsRef)for(const h of c.mount)B(h,y(u,h));else a={}}l=s.shouldUnregister?i.keepDefaultValues?M(n):{}:M(u),m.array.next({values:{...u}}),m.state.next({values:{...u}})}c={mount:i.keepDirtyValues?c.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},f.mount=!V.isValid||!!i.keepIsValid||!!i.keepDirtyValues,f.watch=!!s.shouldUnregister,m.state.next({submitCount:i.keepSubmitCount?t.submitCount:0,isDirty:d?!1:i.keepDirty?t.isDirty:!!(i.keepDefaultValues&&!re(r,n)),isSubmitted:i.keepIsSubmitted?t.isSubmitted:!1,dirtyFields:d?{}:i.keepDirtyValues?i.keepDefaultValues&&l?de(n,l):t.dirtyFields:i.keepDefaultValues&&r?de(n,r):i.keepDirty?t.dirtyFields:{},touchedFields:i.keepTouched?t.touchedFields:{},errors:i.keepErrors?t.errors:{},isSubmitSuccessful:i.keepIsSubmitSuccessful?t.isSubmitSuccessful:!1,isSubmitting:!1})},Ge=(r,i)=>ze(z(r)?r(l):r,i),kt=(r,i={})=>{const o=y(a,r),g=o&&o._f;if(g){const d=g.refs?g.refs[0]:g.ref;d.focus&&(d.focus(),i.shouldSelect&&z(d.select)&&d.select())}},Ct=r=>{t={...t,...r}},Ye={control:{register:Ce,unregister:ke,getFieldState:pe,handleSubmit:Ke,setError:He,_subscribe:$e,_runSchema:ee,_focusError:Le,_getWatch:_,_getDirty:G,_setValid:E,_setFieldArray:Q,_setDisabledField:je,_setErrors:$,_getFieldArray:A,_reset:ze,_resetDefaultValues:()=>z(s.defaultValues)&&s.defaultValues().then(r=>{Ge(r,s.resetOptions),m.state.next({isLoading:!1})}),_removeUnmounted:ie,_disableForm:St,_subjects:m,_proxyFormState:V,get _fields(){return a},get _formValues(){return l},get _state(){return f},set _state(r){f=r},get _defaultValues(){return n},get _names(){return c},set _names(r){c=r},get _formState(){return t},get _options(){return s},set _options(r){s={...s,...r}}},subscribe:Dt,trigger:le,register:Ce,handleSubmit:Ke,watch:wt,setValue:B,getValues:me,reset:Ge,resetField:Et,clearErrors:At,unregister:ke,setError:He,setFocus:kt,getFieldState:pe};return{...Ye,formControl:Ye}}function ir(e={}){const s=F.useRef(void 0),t=F.useRef(void 0),[a,n]=F.useState({isDirty:!1,isValidating:!1,isLoading:z(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:z(e.defaultValues)?void 0:e.defaultValues});if(!s.current)if(e.formControl)s.current={...e.formControl,formState:a},e.defaultValues&&!z(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{const{formControl:f,...c}=er(e);s.current={...c,formState:a}}const l=s.current.control;return l._options=e,Be(()=>{const f=l._subscribe({formState:l._proxyFormState,callback:()=>n({...l._formState}),reRenderRoot:!0});return n(c=>({...c,isReady:!0})),l._formState.isReady=!0,f},[l]),F.useEffect(()=>l._disableForm(e.disabled),[l,e.disabled]),F.useEffect(()=>{e.mode&&(l._options.mode=e.mode),e.reValidateMode&&(l._options.reValidateMode=e.reValidateMode)},[l,e.mode,e.reValidateMode]),F.useEffect(()=>{e.errors&&(l._setErrors(e.errors),l._focusError())},[l,e.errors]),F.useEffect(()=>{e.shouldUnregister&&l._subjects.state.next({values:l._getWatch()})},[l,e.shouldUnregister]),F.useEffect(()=>{if(l._proxyFormState.isDirty){const f=l._getDirty();f!==a.isDirty&&l._subjects.state.next({isDirty:f})}},[l,a.isDirty]),F.useEffect(()=>{e.values&&!re(e.values,t.current)?(l._reset(e.values,{keepFieldsRef:!0,...l._options.resetOptions}),t.current=e.values,n(f=>({...f}))):l._resetDefaultValues()},[l,e.values]),F.useEffect(()=>{l._state.mount||(l._setValid(),l._state.mount=!0),l._state.watch&&(l._state.watch=!1,l._subjects.state.next({...l._formState})),l._removeUnmounted()}),s.current.formState=ct(a,l),s.current}const ar=Nt,Vt=ne.createContext({}),nr=({...e})=>se.jsx(Vt.Provider,{value:{name:e.name},children:se.jsx(Wt,{...e})}),Se=()=>{const e=ne.useContext(Vt),s=ne.useContext(xt),{getFieldState:t}=De(),a=yt({name:e.name}),n=t(e.name,a);if(!e)throw new Error("useFormField should be used within <FormField>");const{id:l}=s;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...n}},xt=ne.createContext({});function lr({className:e,...s}){const t=ne.useId();return se.jsx(xt.Provider,{value:{id:t},children:se.jsx("div",{"data-slot":"form-item",className:Ae("grid gap-2",e),...s})})}function or({className:e,...s}){const{error:t,formItemId:a}=Se();return se.jsx(Mt,{"data-slot":"form-label","data-error":!!t,className:Ae("data-[error=true]:text-destructive",e),htmlFor:a,...s})}function ur({...e}){const{error:s,formItemId:t,formDescriptionId:a,formMessageId:n}=Se();return se.jsx(It,{"data-slot":"form-control",id:t,"aria-describedby":s?`${a} ${n}`:`${a}`,"aria-invalid":!!s,...e})}function dr({className:e,...s}){const{formDescriptionId:t}=Se();return se.jsx("p",{"data-slot":"form-description",id:t,className:Ae("text-muted-foreground text-sm",e),...s})}function fr({className:e,...s}){const{error:t,formMessageId:a}=Se(),n=t?String((t==null?void 0:t.message)??""):s.children;return n?se.jsx("p",{"data-slot":"form-message",id:a,className:Ae("text-destructive text-sm",e),...s,children:n}):null}export{ar as F,nr as a,lr as b,or as c,ur as d,fr as e,dr as f,y as g,qt as h,D as s,ir as u};
