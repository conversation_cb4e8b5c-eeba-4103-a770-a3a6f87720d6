import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ChevronDown, Calendar, X } from 'lucide-react';
import { useState } from 'react';

interface DateRangeFilterProps {
  startDate?: string;
  endDate?: string;
  onDateRangeChange: (startDate: string | undefined, endDate: string | undefined) => void;
}

export const DateRangeFilter = ({ startDate, endDate, onDateRangeChange }: DateRangeFilterProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [localStartDate, setLocalStartDate] = useState(startDate || '');
  const [localEndDate, setLocalEndDate] = useState(endDate || '');

  const hasDateFilter = startDate || endDate;

  const handleApply = () => {
    onDateRangeChange(
      localStartDate || undefined,
      localEndDate || undefined
    );
    setIsOpen(false);
  };

  const handleClear = () => {
    setLocalStartDate('');
    setLocalEndDate('');
    onDateRangeChange(undefined, undefined);
    setIsOpen(false);
  };

  const handlePresetSelect = (preset: string) => {
    const today = new Date();
    let start: string | undefined;
    let end: string | undefined;

    switch (preset) {
      case 'today':
        start = today.toISOString().split('T')[0];
        end = today.toISOString().split('T')[0];
        break;
      case 'last7days':
        start = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        end = today.toISOString().split('T')[0];
        break;
      case 'last30days':
        start = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        end = today.toISOString().split('T')[0];
        break;
      case 'last90days':
        start = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        end = today.toISOString().split('T')[0];
        break;
      default:
        break;
    }

    if (start && end) {
      setLocalStartDate(start);
      setLocalEndDate(end);
      onDateRangeChange(start, end);
      setIsOpen(false);
    }
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" className="gap-2">
          <Calendar className="h-4 w-4" />
          Khoảng thời gian
          {hasDateFilter && (
            <Badge variant="secondary" className="ml-1 px-1 py-0">
              1
            </Badge>
          )}
          <ChevronDown className="h-4 w-4" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-4" align="start">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium">Khoảng thời gian</h4>
            {hasDateFilter && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClear}
                className="h-6 px-2 text-xs"
              >
                <X className="h-3 w-3 mr-1" />
                Xóa
              </Button>
            )}
          </div>

          {/* Preset options */}
          <div className="space-y-2">
            <Label className="text-xs font-medium text-gray-600">Tùy chọn nhanh</Label>
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePresetSelect('today')}
                className="text-xs"
              >
                Hôm nay
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePresetSelect('last7days')}
                className="text-xs"
              >
                7 ngày qua
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePresetSelect('last30days')}
                className="text-xs"
              >
                30 ngày qua
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePresetSelect('last90days')}
                className="text-xs"
              >
                90 ngày qua
              </Button>
            </div>
          </div>

          {/* Custom date range */}
          <div className="space-y-3">
            <Label className="text-xs font-medium text-gray-600">Tùy chọn thời gian</Label>
            <div className="space-y-2">
              <div>
                <Label htmlFor="start-date" className="text-sm">Từ ngày</Label>
                <Input
                  id="start-date"
                  type="date"
                  value={localStartDate}
                  onChange={(e) => setLocalStartDate(e.target.value)}
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="end-date" className="text-sm">Đến ngày</Label>
                <Input
                  id="end-date"
                  type="date"
                  value={localEndDate}
                  onChange={(e) => setLocalEndDate(e.target.value)}
                  className="mt-1"
                />
              </div>
            </div>
          </div>

          <div className="flex gap-2">
            <Button onClick={handleApply} className="flex-1">
              Áp dụng
            </Button>
            <Button variant="outline" onClick={() => setIsOpen(false)} className="flex-1">
              Hủy
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}; 