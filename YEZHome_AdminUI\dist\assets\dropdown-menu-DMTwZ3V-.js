import{c as Mn,t as me,$ as Se,r as a,v as le,j as s,x as N,y as g,w as U,o as De,a0 as ve,n as ye,q as hn,A as xn,z as oe,C as _n,D as Cn,a1 as Rn,H as In,E as En,G as bn,I as Sn,J as Dn,K as yn,M as Pn,a2 as Pe,N as Te}from"./index-DFXNGaQ4.js";import{u as Ae}from"./index-BpksVOb4.js";/**
 * @license lucide-react v0.476.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tn=[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]],lo=Mn("Ellipsis",Tn);var de="rovingFocusGroup.onEntryFocus",An={bubbles:!1,cancelable:!0},W="RovingFocusGroup",[fe,Ne,Nn]=Se(W),[On,Oe]=me(W,[Nn]),[jn,Fn]=On(W),je=a.forwardRef((e,t)=>s.jsx(fe.Provider,{scope:e.__scopeRovingFocusGroup,children:s.jsx(fe.Slot,{scope:e.__scopeRovingFocusGroup,children:s.jsx(kn,{...e,ref:t})})}));je.displayName=W;var kn=a.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:o,loop:r=!1,dir:c,currentTabStopId:d,defaultCurrentTabStopId:l,onCurrentTabStopIdChange:m,onEntryFocus:v,preventScrollOnEntryFocus:i=!1,...u}=e,f=a.useRef(null),M=U(t,f),w=Ae(c),[I,h]=De({prop:d,defaultProp:l??null,onChange:m,caller:W}),[R,T]=a.useState(!1),x=ve(v),S=Ne(n),$=a.useRef(!1),[Z,O]=a.useState(0);return a.useEffect(()=>{const _=f.current;if(_)return _.addEventListener(de,x),()=>_.removeEventListener(de,x)},[x]),s.jsx(jn,{scope:n,orientation:o,dir:w,loop:r,currentTabStopId:I,onItemFocus:a.useCallback(_=>h(_),[h]),onItemShiftTab:a.useCallback(()=>T(!0),[]),onFocusableItemAdd:a.useCallback(()=>O(_=>_+1),[]),onFocusableItemRemove:a.useCallback(()=>O(_=>_-1),[]),children:s.jsx(N.div,{tabIndex:R||Z===0?-1:0,"data-orientation":o,...u,ref:M,style:{outline:"none",...e.style},onMouseDown:g(e.onMouseDown,()=>{$.current=!0}),onFocus:g(e.onFocus,_=>{const G=!$.current;if(_.target===_.currentTarget&&G&&!R){const j=new CustomEvent(de,An);if(_.currentTarget.dispatchEvent(j),!j.defaultPrevented){const B=S().filter(y=>y.focusable),V=B.find(y=>y.active),Q=B.find(y=>y.id===I),ce=[V,Q,...B].filter(Boolean).map(y=>y.ref.current);Ge(ce,i)}}$.current=!1}),onBlur:g(e.onBlur,()=>T(!1))})})}),Fe="RovingFocusGroupItem",ke=a.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:o=!0,active:r=!1,tabStopId:c,children:d,...l}=e,m=le(),v=c||m,i=Fn(Fe,n),u=i.currentTabStopId===v,f=Ne(n),{onFocusableItemAdd:M,onFocusableItemRemove:w,currentTabStopId:I}=i;return a.useEffect(()=>{if(o)return M(),()=>w()},[o,M,w]),s.jsx(fe.ItemSlot,{scope:n,id:v,focusable:o,active:r,children:s.jsx(N.span,{tabIndex:u?0:-1,"data-orientation":i.orientation,...l,ref:t,onMouseDown:g(e.onMouseDown,h=>{o?i.onItemFocus(v):h.preventDefault()}),onFocus:g(e.onFocus,()=>i.onItemFocus(v)),onKeyDown:g(e.onKeyDown,h=>{if(h.key==="Tab"&&h.shiftKey){i.onItemShiftTab();return}if(h.target!==h.currentTarget)return;const R=Kn(h,i.orientation,i.dir);if(R!==void 0){if(h.metaKey||h.ctrlKey||h.altKey||h.shiftKey)return;h.preventDefault();let x=f().filter(S=>S.focusable).map(S=>S.ref.current);if(R==="last")x.reverse();else if(R==="prev"||R==="next"){R==="prev"&&x.reverse();const S=x.indexOf(h.currentTarget);x=i.loop?Un(x,S+1):x.slice(S+1)}setTimeout(()=>Ge(x))}}),children:typeof d=="function"?d({isCurrentTabStop:u,hasTabStop:I!=null}):d})})});ke.displayName=Fe;var Gn={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Ln(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function Kn(e,t,n){const o=Ln(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(o))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(o)))return Gn[o]}function Ge(e,t=!1){const n=document.activeElement;for(const o of e)if(o===n||(o.focus({preventScroll:t}),document.activeElement!==n))return}function Un(e,t){return e.map((n,o)=>e[(t+o)%e.length])}var $n=je,Bn=ke,pe=["Enter"," "],Vn=["ArrowDown","PageUp","Home"],Le=["ArrowUp","PageDown","End"],Yn=[...Vn,...Le],zn={ltr:[...pe,"ArrowRight"],rtl:[...pe,"ArrowLeft"]},Xn={ltr:["ArrowLeft"],rtl:["ArrowRight"]},q="Menu",[X,Hn,Wn]=Se(q),[F,Ke]=me(q,[Wn,ye,Oe]),re=ye(),Ue=Oe(),[qn,k]=F(q),[Jn,J]=F(q),$e=e=>{const{__scopeMenu:t,open:n=!1,children:o,dir:r,onOpenChange:c,modal:d=!0}=e,l=re(t),[m,v]=a.useState(null),i=a.useRef(!1),u=ve(c),f=Ae(r);return a.useEffect(()=>{const M=()=>{i.current=!0,document.addEventListener("pointerdown",w,{capture:!0,once:!0}),document.addEventListener("pointermove",w,{capture:!0,once:!0})},w=()=>i.current=!1;return document.addEventListener("keydown",M,{capture:!0}),()=>{document.removeEventListener("keydown",M,{capture:!0}),document.removeEventListener("pointerdown",w,{capture:!0}),document.removeEventListener("pointermove",w,{capture:!0})}},[]),s.jsx(hn,{...l,children:s.jsx(qn,{scope:t,open:n,onOpenChange:u,content:m,onContentChange:v,children:s.jsx(Jn,{scope:t,onClose:a.useCallback(()=>u(!1),[u]),isUsingKeyboardRef:i,dir:f,modal:d,children:o})})})};$e.displayName=q;var Zn="MenuAnchor",ge=a.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e,r=re(n);return s.jsx(xn,{...r,...o,ref:t})});ge.displayName=Zn;var we="MenuPortal",[Qn,Be]=F(we,{forceMount:void 0}),Ve=e=>{const{__scopeMenu:t,forceMount:n,children:o,container:r}=e,c=k(we,t);return s.jsx(Qn,{scope:t,forceMount:n,children:s.jsx(oe,{present:n||c.open,children:s.jsx(_n,{asChild:!0,container:r,children:o})})})};Ve.displayName=we;var b="MenuContent",[et,Me]=F(b),Ye=a.forwardRef((e,t)=>{const n=Be(b,e.__scopeMenu),{forceMount:o=n.forceMount,...r}=e,c=k(b,e.__scopeMenu),d=J(b,e.__scopeMenu);return s.jsx(X.Provider,{scope:e.__scopeMenu,children:s.jsx(oe,{present:o||c.open,children:s.jsx(X.Slot,{scope:e.__scopeMenu,children:d.modal?s.jsx(nt,{...r,ref:t}):s.jsx(tt,{...r,ref:t})})})})}),nt=a.forwardRef((e,t)=>{const n=k(b,e.__scopeMenu),o=a.useRef(null),r=U(t,o);return a.useEffect(()=>{const c=o.current;if(c)return Cn(c)},[]),s.jsx(he,{...e,ref:r,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:g(e.onFocusOutside,c=>c.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),tt=a.forwardRef((e,t)=>{const n=k(b,e.__scopeMenu);return s.jsx(he,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),ot=bn("MenuContent.ScrollLock"),he=a.forwardRef((e,t)=>{const{__scopeMenu:n,loop:o=!1,trapFocus:r,onOpenAutoFocus:c,onCloseAutoFocus:d,disableOutsidePointerEvents:l,onEntryFocus:m,onEscapeKeyDown:v,onPointerDownOutside:i,onFocusOutside:u,onInteractOutside:f,onDismiss:M,disableOutsideScroll:w,...I}=e,h=k(b,n),R=J(b,n),T=re(n),x=Ue(n),S=Hn(n),[$,Z]=a.useState(null),O=a.useRef(null),_=U(t,O,h.onContentChange),G=a.useRef(0),j=a.useRef(""),B=a.useRef(0),V=a.useRef(null),Q=a.useRef("right"),ee=a.useRef(0),ce=w?En:a.Fragment,y=w?{as:ot,allowPinchZoom:!0}:void 0,wn=p=>{var K,Ie;const E=j.current+p,D=S().filter(P=>!P.disabled),A=document.activeElement,ue=(K=D.find(P=>P.ref.current===A))==null?void 0:K.textValue,ie=D.map(P=>P.textValue),Re=vt(ie,E,ue),Y=(Ie=D.find(P=>P.textValue===Re))==null?void 0:Ie.ref.current;(function P(Ee){j.current=Ee,window.clearTimeout(G.current),Ee!==""&&(G.current=window.setTimeout(()=>P(""),1e3))})(E),Y&&setTimeout(()=>Y.focus())};a.useEffect(()=>()=>window.clearTimeout(G.current),[]),In();const L=a.useCallback(p=>{var D,A;return Q.current===((D=V.current)==null?void 0:D.side)&&wt(p,(A=V.current)==null?void 0:A.area)},[]);return s.jsx(et,{scope:n,searchRef:j,onItemEnter:a.useCallback(p=>{L(p)&&p.preventDefault()},[L]),onItemLeave:a.useCallback(p=>{var E;L(p)||((E=O.current)==null||E.focus(),Z(null))},[L]),onTriggerLeave:a.useCallback(p=>{L(p)&&p.preventDefault()},[L]),pointerGraceTimerRef:B,onPointerGraceIntentChange:a.useCallback(p=>{V.current=p},[]),children:s.jsx(ce,{...y,children:s.jsx(Sn,{asChild:!0,trapped:r,onMountAutoFocus:g(c,p=>{var E;p.preventDefault(),(E=O.current)==null||E.focus({preventScroll:!0})}),onUnmountAutoFocus:d,children:s.jsx(Dn,{asChild:!0,disableOutsidePointerEvents:l,onEscapeKeyDown:v,onPointerDownOutside:i,onFocusOutside:u,onInteractOutside:f,onDismiss:M,children:s.jsx($n,{asChild:!0,...x,dir:R.dir,orientation:"vertical",loop:o,currentTabStopId:$,onCurrentTabStopIdChange:Z,onEntryFocus:g(m,p=>{R.isUsingKeyboardRef.current||p.preventDefault()}),preventScrollOnEntryFocus:!0,children:s.jsx(yn,{role:"menu","aria-orientation":"vertical","data-state":cn(h.open),"data-radix-menu-content":"",dir:R.dir,...T,...I,ref:_,style:{outline:"none",...I.style},onKeyDown:g(I.onKeyDown,p=>{const D=p.target.closest("[data-radix-menu-content]")===p.currentTarget,A=p.ctrlKey||p.altKey||p.metaKey,ue=p.key.length===1;D&&(p.key==="Tab"&&p.preventDefault(),!A&&ue&&wn(p.key));const ie=O.current;if(p.target!==ie||!Yn.includes(p.key))return;p.preventDefault();const Y=S().filter(K=>!K.disabled).map(K=>K.ref.current);Le.includes(p.key)&&Y.reverse(),pt(Y)}),onBlur:g(e.onBlur,p=>{p.currentTarget.contains(p.target)||(window.clearTimeout(G.current),j.current="")}),onPointerMove:g(e.onPointerMove,H(p=>{const E=p.target,D=ee.current!==p.clientX;if(p.currentTarget.contains(E)&&D){const A=p.clientX>ee.current?"right":"left";Q.current=A,ee.current=p.clientX}}))})})})})})})});Ye.displayName=b;var rt="MenuGroup",xe=a.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e;return s.jsx(N.div,{role:"group",...o,ref:t})});xe.displayName=rt;var at="MenuLabel",ze=a.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e;return s.jsx(N.div,{...o,ref:t})});ze.displayName=at;var ne="MenuItem",be="menu.itemSelect",ae=a.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:o,...r}=e,c=a.useRef(null),d=J(ne,e.__scopeMenu),l=Me(ne,e.__scopeMenu),m=U(t,c),v=a.useRef(!1),i=()=>{const u=c.current;if(!n&&u){const f=new CustomEvent(be,{bubbles:!0,cancelable:!0});u.addEventListener(be,M=>o==null?void 0:o(M),{once:!0}),Rn(u,f),f.defaultPrevented?v.current=!1:d.onClose()}};return s.jsx(Xe,{...r,ref:m,disabled:n,onClick:g(e.onClick,i),onPointerDown:u=>{var f;(f=e.onPointerDown)==null||f.call(e,u),v.current=!0},onPointerUp:g(e.onPointerUp,u=>{var f;v.current||(f=u.currentTarget)==null||f.click()}),onKeyDown:g(e.onKeyDown,u=>{const f=l.searchRef.current!=="";n||f&&u.key===" "||pe.includes(u.key)&&(u.currentTarget.click(),u.preventDefault())})})});ae.displayName=ne;var Xe=a.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:o=!1,textValue:r,...c}=e,d=Me(ne,n),l=Ue(n),m=a.useRef(null),v=U(t,m),[i,u]=a.useState(!1),[f,M]=a.useState("");return a.useEffect(()=>{const w=m.current;w&&M((w.textContent??"").trim())},[c.children]),s.jsx(X.ItemSlot,{scope:n,disabled:o,textValue:r??f,children:s.jsx(Bn,{asChild:!0,...l,focusable:!o,children:s.jsx(N.div,{role:"menuitem","data-highlighted":i?"":void 0,"aria-disabled":o||void 0,"data-disabled":o?"":void 0,...c,ref:v,onPointerMove:g(e.onPointerMove,H(w=>{o?d.onItemLeave(w):(d.onItemEnter(w),w.defaultPrevented||w.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:g(e.onPointerLeave,H(w=>d.onItemLeave(w))),onFocus:g(e.onFocus,()=>u(!0)),onBlur:g(e.onBlur,()=>u(!1))})})})}),st="MenuCheckboxItem",He=a.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:o,...r}=e;return s.jsx(Qe,{scope:e.__scopeMenu,checked:n,children:s.jsx(ae,{role:"menuitemcheckbox","aria-checked":te(n)?"mixed":n,...r,ref:t,"data-state":Ce(n),onSelect:g(r.onSelect,()=>o==null?void 0:o(te(n)?!0:!n),{checkForDefaultPrevented:!1})})})});He.displayName=st;var We="MenuRadioGroup",[ct,ut]=F(We,{value:void 0,onValueChange:()=>{}}),qe=a.forwardRef((e,t)=>{const{value:n,onValueChange:o,...r}=e,c=ve(o);return s.jsx(ct,{scope:e.__scopeMenu,value:n,onValueChange:c,children:s.jsx(xe,{...r,ref:t})})});qe.displayName=We;var Je="MenuRadioItem",Ze=a.forwardRef((e,t)=>{const{value:n,...o}=e,r=ut(Je,e.__scopeMenu),c=n===r.value;return s.jsx(Qe,{scope:e.__scopeMenu,checked:c,children:s.jsx(ae,{role:"menuitemradio","aria-checked":c,...o,ref:t,"data-state":Ce(c),onSelect:g(o.onSelect,()=>{var d;return(d=r.onValueChange)==null?void 0:d.call(r,n)},{checkForDefaultPrevented:!1})})})});Ze.displayName=Je;var _e="MenuItemIndicator",[Qe,it]=F(_e,{checked:!1}),en=a.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:o,...r}=e,c=it(_e,n);return s.jsx(oe,{present:o||te(c.checked)||c.checked===!0,children:s.jsx(N.span,{...r,ref:t,"data-state":Ce(c.checked)})})});en.displayName=_e;var dt="MenuSeparator",nn=a.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e;return s.jsx(N.div,{role:"separator","aria-orientation":"horizontal",...o,ref:t})});nn.displayName=dt;var lt="MenuArrow",tn=a.forwardRef((e,t)=>{const{__scopeMenu:n,...o}=e,r=re(n);return s.jsx(Pn,{...r,...o,ref:t})});tn.displayName=lt;var ft="MenuSub",[fo,on]=F(ft),z="MenuSubTrigger",rn=a.forwardRef((e,t)=>{const n=k(z,e.__scopeMenu),o=J(z,e.__scopeMenu),r=on(z,e.__scopeMenu),c=Me(z,e.__scopeMenu),d=a.useRef(null),{pointerGraceTimerRef:l,onPointerGraceIntentChange:m}=c,v={__scopeMenu:e.__scopeMenu},i=a.useCallback(()=>{d.current&&window.clearTimeout(d.current),d.current=null},[]);return a.useEffect(()=>i,[i]),a.useEffect(()=>{const u=l.current;return()=>{window.clearTimeout(u),m(null)}},[l,m]),s.jsx(ge,{asChild:!0,...v,children:s.jsx(Xe,{id:r.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":r.contentId,"data-state":cn(n.open),...e,ref:Pe(t,r.onTriggerChange),onClick:u=>{var f;(f=e.onClick)==null||f.call(e,u),!(e.disabled||u.defaultPrevented)&&(u.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:g(e.onPointerMove,H(u=>{c.onItemEnter(u),!u.defaultPrevented&&!e.disabled&&!n.open&&!d.current&&(c.onPointerGraceIntentChange(null),d.current=window.setTimeout(()=>{n.onOpenChange(!0),i()},100))})),onPointerLeave:g(e.onPointerLeave,H(u=>{var M,w;i();const f=(M=n.content)==null?void 0:M.getBoundingClientRect();if(f){const I=(w=n.content)==null?void 0:w.dataset.side,h=I==="right",R=h?-5:5,T=f[h?"left":"right"],x=f[h?"right":"left"];c.onPointerGraceIntentChange({area:[{x:u.clientX+R,y:u.clientY},{x:T,y:f.top},{x,y:f.top},{x,y:f.bottom},{x:T,y:f.bottom}],side:I}),window.clearTimeout(l.current),l.current=window.setTimeout(()=>c.onPointerGraceIntentChange(null),300)}else{if(c.onTriggerLeave(u),u.defaultPrevented)return;c.onPointerGraceIntentChange(null)}})),onKeyDown:g(e.onKeyDown,u=>{var M;const f=c.searchRef.current!=="";e.disabled||f&&u.key===" "||zn[o.dir].includes(u.key)&&(n.onOpenChange(!0),(M=n.content)==null||M.focus(),u.preventDefault())})})})});rn.displayName=z;var an="MenuSubContent",sn=a.forwardRef((e,t)=>{const n=Be(b,e.__scopeMenu),{forceMount:o=n.forceMount,...r}=e,c=k(b,e.__scopeMenu),d=J(b,e.__scopeMenu),l=on(an,e.__scopeMenu),m=a.useRef(null),v=U(t,m);return s.jsx(X.Provider,{scope:e.__scopeMenu,children:s.jsx(oe,{present:o||c.open,children:s.jsx(X.Slot,{scope:e.__scopeMenu,children:s.jsx(he,{id:l.contentId,"aria-labelledby":l.triggerId,...r,ref:v,align:"start",side:d.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:i=>{var u;d.isUsingKeyboardRef.current&&((u=m.current)==null||u.focus()),i.preventDefault()},onCloseAutoFocus:i=>i.preventDefault(),onFocusOutside:g(e.onFocusOutside,i=>{i.target!==l.trigger&&c.onOpenChange(!1)}),onEscapeKeyDown:g(e.onEscapeKeyDown,i=>{d.onClose(),i.preventDefault()}),onKeyDown:g(e.onKeyDown,i=>{var M;const u=i.currentTarget.contains(i.target),f=Xn[d.dir].includes(i.key);u&&f&&(c.onOpenChange(!1),(M=l.trigger)==null||M.focus(),i.preventDefault())})})})})})});sn.displayName=an;function cn(e){return e?"open":"closed"}function te(e){return e==="indeterminate"}function Ce(e){return te(e)?"indeterminate":e?"checked":"unchecked"}function pt(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function mt(e,t){return e.map((n,o)=>e[(t+o)%e.length])}function vt(e,t,n){const r=t.length>1&&Array.from(t).every(v=>v===t[0])?t[0]:t,c=n?e.indexOf(n):-1;let d=mt(e,Math.max(c,0));r.length===1&&(d=d.filter(v=>v!==n));const m=d.find(v=>v.toLowerCase().startsWith(r.toLowerCase()));return m!==n?m:void 0}function gt(e,t){const{x:n,y:o}=e;let r=!1;for(let c=0,d=t.length-1;c<t.length;d=c++){const l=t[c],m=t[d],v=l.x,i=l.y,u=m.x,f=m.y;i>o!=f>o&&n<(u-v)*(o-i)/(f-i)+v&&(r=!r)}return r}function wt(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return gt(n,t)}function H(e){return t=>t.pointerType==="mouse"?e(t):void 0}var Mt=$e,ht=ge,xt=Ve,_t=Ye,Ct=xe,Rt=ze,It=ae,Et=He,bt=qe,St=Ze,Dt=en,yt=nn,Pt=tn,Tt=rn,At=sn,se="DropdownMenu",[Nt,po]=me(se,[Ke]),C=Ke(),[Ot,un]=Nt(se),dn=e=>{const{__scopeDropdownMenu:t,children:n,dir:o,open:r,defaultOpen:c,onOpenChange:d,modal:l=!0}=e,m=C(t),v=a.useRef(null),[i,u]=De({prop:r,defaultProp:c??!1,onChange:d,caller:se});return s.jsx(Ot,{scope:t,triggerId:le(),triggerRef:v,contentId:le(),open:i,onOpenChange:u,onOpenToggle:a.useCallback(()=>u(f=>!f),[u]),modal:l,children:s.jsx(Mt,{...m,open:i,onOpenChange:u,dir:o,modal:l,children:n})})};dn.displayName=se;var ln="DropdownMenuTrigger",fn=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:o=!1,...r}=e,c=un(ln,n),d=C(n);return s.jsx(ht,{asChild:!0,...d,children:s.jsx(N.button,{type:"button",id:c.triggerId,"aria-haspopup":"menu","aria-expanded":c.open,"aria-controls":c.open?c.contentId:void 0,"data-state":c.open?"open":"closed","data-disabled":o?"":void 0,disabled:o,...r,ref:Pe(t,c.triggerRef),onPointerDown:g(e.onPointerDown,l=>{!o&&l.button===0&&l.ctrlKey===!1&&(c.onOpenToggle(),c.open||l.preventDefault())}),onKeyDown:g(e.onKeyDown,l=>{o||(["Enter"," "].includes(l.key)&&c.onOpenToggle(),l.key==="ArrowDown"&&c.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(l.key)&&l.preventDefault())})})})});fn.displayName=ln;var jt="DropdownMenuPortal",pn=e=>{const{__scopeDropdownMenu:t,...n}=e,o=C(t);return s.jsx(xt,{...o,...n})};pn.displayName=jt;var mn="DropdownMenuContent",vn=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=un(mn,n),c=C(n),d=a.useRef(!1);return s.jsx(_t,{id:r.contentId,"aria-labelledby":r.triggerId,...c,...o,ref:t,onCloseAutoFocus:g(e.onCloseAutoFocus,l=>{var m;d.current||(m=r.triggerRef.current)==null||m.focus(),d.current=!1,l.preventDefault()}),onInteractOutside:g(e.onInteractOutside,l=>{const m=l.detail.originalEvent,v=m.button===0&&m.ctrlKey===!0,i=m.button===2||v;(!r.modal||i)&&(d.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});vn.displayName=mn;var Ft="DropdownMenuGroup",kt=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=C(n);return s.jsx(Ct,{...r,...o,ref:t})});kt.displayName=Ft;var Gt="DropdownMenuLabel",Lt=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=C(n);return s.jsx(Rt,{...r,...o,ref:t})});Lt.displayName=Gt;var Kt="DropdownMenuItem",gn=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=C(n);return s.jsx(It,{...r,...o,ref:t})});gn.displayName=Kt;var Ut="DropdownMenuCheckboxItem",$t=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=C(n);return s.jsx(Et,{...r,...o,ref:t})});$t.displayName=Ut;var Bt="DropdownMenuRadioGroup",Vt=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=C(n);return s.jsx(bt,{...r,...o,ref:t})});Vt.displayName=Bt;var Yt="DropdownMenuRadioItem",zt=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=C(n);return s.jsx(St,{...r,...o,ref:t})});zt.displayName=Yt;var Xt="DropdownMenuItemIndicator",Ht=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=C(n);return s.jsx(Dt,{...r,...o,ref:t})});Ht.displayName=Xt;var Wt="DropdownMenuSeparator",qt=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=C(n);return s.jsx(yt,{...r,...o,ref:t})});qt.displayName=Wt;var Jt="DropdownMenuArrow",Zt=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=C(n);return s.jsx(Pt,{...r,...o,ref:t})});Zt.displayName=Jt;var Qt="DropdownMenuSubTrigger",eo=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=C(n);return s.jsx(Tt,{...r,...o,ref:t})});eo.displayName=Qt;var no="DropdownMenuSubContent",to=a.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...o}=e,r=C(n);return s.jsx(At,{...r,...o,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});to.displayName=no;var oo=dn,ro=fn,ao=pn,so=vn,co=gn;function mo({...e}){return s.jsx(oo,{"data-slot":"dropdown-menu",...e})}function vo({...e}){return s.jsx(ro,{"data-slot":"dropdown-menu-trigger",...e})}function go({className:e,sideOffset:t=4,...n}){return s.jsx(ao,{children:s.jsx(so,{"data-slot":"dropdown-menu-content",sideOffset:t,className:Te("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...n})})}function wo({className:e,inset:t,variant:n="default",...o}){return s.jsx(co,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":n,className:Te("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...o})}export{mo as D,lo as E,vo as a,go as b,wo as c};
