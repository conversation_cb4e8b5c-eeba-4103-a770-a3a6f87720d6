import { useForm } from 'react-hook-form'
import { Search } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Form } from '@/components/ui/form'

interface EmployeeSearchFormData {
  email?: string
  name?: string
  phone?: string
}

interface EmployeeSearchFormProps {
  defaultValues?: EmployeeSearchFormData
  onSubmit: (data: EmployeeSearchFormData) => void
}

export const EmployeeSearchForm = ({ defaultValues, onSubmit }: EmployeeSearchFormProps) => {
  const form = useForm<EmployeeSearchFormData>({
    defaultValues: defaultValues || {
      email: '',
      name: '',
      phone: '',
    },
  })

  return (
    <Card>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="searchemail">Email</Label>
                <Input
                  id="searchemail"
                  placeholder="Nhập email"
                  {...form.register('email')}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="searchname">Họ tên</Label>
                <Input
                  id="searchname"
                  placeholder="Nhập họ tên"
                  {...form.register('name')}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="searchphone">Số điện thoại</Label>
                <Input
                  id="searchphone"
                  placeholder="Nhập số điện thoại"
                  {...form.register('phone')}
                />
              </div>
            </div>
            <div className="flex justify-end">
              <Button type="submit">
                <Search className="mr-2 h-4 w-4" />
                Tìm kiếm
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
} 