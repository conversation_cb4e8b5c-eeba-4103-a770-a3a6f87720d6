import type { NOTIFICATION_TYPE } from "../enum";

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  relatedEntityId?: string;
  relatedPropertyId?: string;
  actionUrl?: string;
  isRead: boolean;
  createdAt: string;
  updatedAt: string;
}

export type NotificationType = typeof NOTIFICATION_TYPE.SYSTEM | 
                               typeof NOTIFICATION_TYPE.PROMOTION | 
                               typeof NOTIFICATION_TYPE.NEWS;

export interface NotificationCreateRequest {
  type: NotificationType;
  title: string;
  message: string;
  relatedEntityId?: string;
  relatedPropertyId?: string;
  actionUrl?: string;
}

export interface NotificationFilter {
  page: number;
  pageSize: number;
  type?: NotificationType;
  startDate?: string;
  endDate?: string;
}

export interface PagedResultDto<T> {
  items: T[];
  totalCount: number;
  pageCount: number;
  currentPage: number;
  pageSize: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
}

// Notification type display mapping
export const notificationTypeMap: Record<NotificationType, string> = {
  System: 'Hệ thống',
  Promotion: '<PERSON><PERSON><PERSON><PERSON><PERSON> mãi',
  News: 'Tin tức',
}

// Notification type color mapping for UI
export const notificationTypeColorMap: Record<NotificationType, string> = {
  System: 'bg-blue-50 text-blue-700 border-blue-200',
  Promotion: 'bg-orange-50 text-orange-700 border-orange-200',
  News: 'bg-green-50 text-green-700 border-green-200',
} 