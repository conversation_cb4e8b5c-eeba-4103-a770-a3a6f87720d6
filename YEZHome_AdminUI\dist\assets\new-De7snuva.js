import{a as c,as as m,a7 as l,m as d,Y as e,j as t,B as u}from"./index-DFXNGaQ4.js";import{b as h}from"./blog-service-Dpp8ABmX.js";import{B as p}from"./blog-post-form-BWQUA0yd.js";import{C as x,a as g,b as j,d as v}from"./card-Dikj3j-6.js";import{A as b}from"./arrow-left-CbfRM3Sk.js";import"./form-lBragMB2.js";import"./label-BscdIpbp.js";import"./zod-TzqjP9W3.js";import"./input-D3ozjVi3.js";import"./index-BmxYfgzo.js";import"./select-DIbexXwn.js";import"./index-BpksVOb4.js";import"./chevron-down-DuS99v9n.js";function f(){const r=c(),{user:i}=m(),{history:n}=l(),o=d({mutationFn:s=>h.createBlogPost({...s,authorID:(i==null?void 0:i.id)||""}),onSuccess:()=>{e({title:"Thành công",description:"Bài viết đã được tạo thành công.",variant:"success"}),r({to:"/blog"})},onError:s=>{e({title:"Lỗi",description:"Không thể tạo bài viết. Vui lòng thử lại sau.",variant:"destructive"}),console.error("Error creating blog post:",s)}}),a=s=>{o.mutate(s)};return t.jsxs("div",{className:"space-y-6",children:[t.jsx("div",{className:"flex items-center justify-between",children:t.jsxs("div",{className:"flex items-center gap-4",children:[t.jsx(u,{variant:"outline",size:"icon",onClick:()=>{n.go(-1)},children:t.jsx(b,{className:"h-4 w-4"})}),t.jsx("h1",{className:"text-2xl font-bold",children:"Tạo bài viết mới"})]})}),t.jsxs(x,{children:[t.jsx(g,{children:t.jsx(j,{className:"text-xl",children:"Thông tin bài viết"})}),t.jsx(v,{children:t.jsx(p,{onSubmit:a,isSubmitting:o.isPending})})]})]})}const R=f;export{R as component};
