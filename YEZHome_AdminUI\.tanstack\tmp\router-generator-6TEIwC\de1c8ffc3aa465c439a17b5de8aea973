import { createFileRoute, <PERSON> } from "@tanstack/react-router";
import { BlogDetailPage } from "@/features/blog-management/components/BlogDetailPage";
import { Loading } from "@/components/ui/loading";

export const Route = createFileRoute("/_authenticated/blog/$blogId/$blogId")({
  component: Detail,
  beforeLoad: () => {
    return {
      getTitle: () => 'Chi tiết tin tức'
    };
  },
  pendingComponent: () => <Loading />,
  errorComponent: () => <div className="p-10">Đ<PERSON> xảy ra lỗi khi tải dữ liệu.</div>,
});

function Detail() {
  const { blogId } = Route.useParams();
  return (
    <div>
      <h1><Link to="/blog/$blogId/edit" params={{ blogId: blogId }}>Edit</Link></h1>
    </div>
  )
}