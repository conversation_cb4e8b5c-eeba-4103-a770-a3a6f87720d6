import{N as n,j as o,au as d}from"./index-DFXNGaQ4.js";const i=d("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",subtle:"border-transparent",outline:"text-foreground"},colorScheme:{blue:"bg-blue-100 text-blue-800",green:"bg-green-100 text-green-800",yellow:"bg-yellow-100 text-yellow-800",red:"bg-red-100 text-red-800",gray:"bg-gray-100 text-gray-800",purple:"bg-purple-100 text-purple-800",orange:"bg-orange-100 text-orange-800"},size:{default:"h-fit",withDot:"h-fit gap-1"}},defaultVariants:{variant:"default",size:"default"}});function f({className:s,variant:e,colorScheme:r,showDot:t,dotColor:g,...a}){const l=n("block h-2 w-2 rounded-full",(u=>{switch(u){case"blue":return"bg-blue-500";case"green":return"bg-green-500";case"yellow":return"bg-yellow-500";case"red":return"bg-red-500";case"gray":return"bg-gray-500";case"purple":return"bg-purple-500";case"orange":return"bg-orange-500";default:return"bg-current"}})(g||r));return o.jsxs("div",{className:n(i({variant:e,colorScheme:e==="subtle"?r||"gray":void 0,size:t?"withDot":"default"}),s),...a,children:[t&&o.jsx("span",{className:l}),a.children]})}export{f as B};
